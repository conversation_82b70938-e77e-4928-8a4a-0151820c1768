'use client';

import React, { useState } from 'react';
import { Brain, Loader2, AlertCircle, CheckCircle, ChevronDown, ChevronUp } from 'lucide-react';
import { analyzeDocument, DocumentAnalysisResult } from '@/lib/documentAnalysisService';

interface DocumentAnalysisButtonProps {
  pdfUrl?: string;
  onAnalysisComplete?: (result: DocumentAnalysisResult) => void;
  className?: string;
}

export default function DocumentAnalysisButton({
  pdfUrl,
  onAnalysisComplete,
  className = ''
}: DocumentAnalysisButtonProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<DocumentAnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    classification: true,
    financial: true,
    company: true,
    summary: true,
    extracted: false,
    regulatory: false,
    investment: false
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleAnalyze = async () => {
    if (!pdfUrl) {
      setError('No PDF URL available for analysis');
      return;
    }

    setIsAnalyzing(true);
    setError(null);
    setAnalysisResult(null);

    try {
      console.log('🚀 Starting NEW METHOD analysis for:', pdfUrl);
      const result = await analyzeDocument(pdfUrl);
      console.log('✅ NEW METHOD analysis complete:', result);
      setAnalysisResult(result);
      onAnalysisComplete?.(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'New method analysis failed';
      console.error('❌ NEW METHOD analysis failed:', err);
      setError(errorMessage);
    } finally {
      setIsAnalyzing(false);
    }
  };

  if (!pdfUrl) {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Analysis Button */}
      <button
        onClick={handleAnalyze}
        disabled={isAnalyzing}
        className={`
          inline-flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-lg
          transition-colors duration-200
          ${isAnalyzing 
            ? 'bg-blue-50 text-blue-600 cursor-not-allowed' 
            : 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800'
          }
          ${analysisResult?.status === 'success' ? 'bg-green-600 hover:bg-green-700' : ''}
          ${error ? 'bg-red-600 hover:bg-red-700' : ''}
        `}
      >
        {isAnalyzing ? (
          <>
            <Loader2 className="w-4 h-4 animate-spin" />
            Analyzing...
          </>
        ) : analysisResult?.status === 'success' ? (
          <>
            <CheckCircle className="w-4 h-4" />
            Analysis Complete
          </>
        ) : error ? (
          <>
            <AlertCircle className="w-4 h-4" />
            Analysis Failed
          </>
        ) : (
          <>
            <Brain className="w-4 h-4" />
            Complete AI Analysis
          </>
        )}
      </button>

      {/* Error Display */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start gap-2">
            <AlertCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-red-800">Analysis Failed</p>
              <p className="text-sm text-red-600 mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* NEW METHOD Analysis Results */}
      {analysisResult && analysisResult.status === 'success' && (
        <div className="bg-white border border-slate-200 rounded-lg shadow-sm">
          {/* Header */}
          <div className="p-4 bg-green-50 border-b border-green-200">
            <div className="flex items-start gap-2">
              <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-sm font-medium text-green-800">NEW METHOD: Complete Analysis</p>
                <p className="text-xs text-green-600 mt-1">
                  Company: {analysisResult.company_name} • Type: {analysisResult.document_type}
                </p>
                <p className="text-xs text-green-600">
                  Processed: {(analysisResult as any).processed_with || 'new_method_complete'}
                </p>
              </div>
            </div>
          </div>

          <div className="p-4 space-y-4">
            {/* Document Classification - NEW FORMAT ONLY */}
            {(analysisResult as any).summary?.categories && (
              <div className="border border-slate-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => toggleSection('classification')}
                  className="w-full px-3 py-2 bg-slate-50 hover:bg-slate-100 flex items-center justify-between text-left transition-colors"
                >
                  <span className="text-sm font-medium text-slate-900">Document Classification</span>
                  {expandedSections.classification ? (
                    <ChevronUp className="w-4 h-4 text-slate-500" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-slate-500" />
                  )}
                </button>
                {expandedSections.classification && (
                  <div className="p-3 bg-white">
                    <div className="space-y-2">
                      <div>
                        <span className="text-xs font-medium text-slate-700">Categories:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {(((analysisResult as any).summary?.categories) || []).map((category: string, index: number) => (
                            <span key={index} className="px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded">
                              {category}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div>
                        <span className="text-xs font-medium text-slate-700">Primary:</span>
                        <span className="ml-2 px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded">
                          {(analysisResult as any).summary?.primary_category || 'N/A'}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Financial Key Insights - Support both old and new formats */}
            {((analysisResult as any).summary?.extracted_data?.quarterly_results ||
              (analysisResult as any).summary?.financial_metrics) && (
              <div className="border border-slate-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => toggleSection('financial')}
                  className="w-full px-3 py-2 bg-slate-50 hover:bg-slate-100 flex items-center justify-between text-left transition-colors"
                >
                  <span className="text-sm font-medium text-slate-900">Key Financial Insights</span>
                  {expandedSections.financial ? (
                    <ChevronUp className="w-4 h-4 text-slate-500" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-slate-500" />
                  )}
                </button>
                {expandedSections.financial && (
                  <div className="p-3 bg-white space-y-3">
                    {(() => {
                      // Support both old and new formats
                      const summaryData = (analysisResult as any).summary || {};
                      const qrData = summaryData.extracted_data?.quarterly_results || {};
                      const financialMetrics = summaryData.financial_metrics || {};

                      // Determine which format to use
                      const isNewFormat = !!financialMetrics.revenue || !!summaryData.key_disclosures;

                      // Debug logging
                      console.log('Financial Debug:', {
                        fullAnalysisResult: analysisResult,
                        summaryData: summaryData,
                        hasExtractedData: !!summaryData.extracted_data,
                        hasQuarterlyResults: !!summaryData.extracted_data?.quarterly_results,
                        hasFinancialMetrics: !!summaryData.financial_metrics,
                        hasKeyDisclosures: !!summaryData.key_disclosures,
                        keyDisclosuresData: summaryData.key_disclosures,
                        newFormatKeyDisclosures: summaryData.new_format_data?.key_disclosures,
                        isNewFormat: isNewFormat,
                        qrData: qrData,
                        financialMetrics: financialMetrics
                      });

                      // Additional debug for key highlights section
                      console.log('Key Highlights Debug:', {
                        shouldShowSection: ((qrData.key_financial_highlights && qrData.key_financial_highlights.length > 0) ||
                          (summaryData.key_disclosures && summaryData.key_disclosures.key_points) ||
                          (summaryData.new_format_data?.key_disclosures && summaryData.new_format_data.key_disclosures.key_points)),
                        hasQrHighlights: !!(qrData.key_financial_highlights && qrData.key_financial_highlights.length > 0),
                        hasMainKeyDisclosures: !!(summaryData.key_disclosures && summaryData.key_disclosures.key_points),
                        hasNestedKeyDisclosures: !!(summaryData.new_format_data?.key_disclosures && summaryData.new_format_data.key_disclosures.key_points),
                        mainKeyPoints: summaryData.key_disclosures?.key_points,
                        nestedKeyPoints: summaryData.new_format_data?.key_disclosures?.key_points,
                        isNewFormat: isNewFormat
                      });

                      // Helper function to clean and format key points
                      const formatKeyPoints = (keyPoints: string): string => {
                        if (!keyPoints) return '';

                        let formatted = keyPoints;

                        // Remove redundant phrases since content is already in highlighted box
                        formatted = formatted.replace(/^Key Information:?\s*/i, '');
                        formatted = formatted.replace(/^Key Highlights:?\s*/i, '');
                        formatted = formatted.replace(/^Key Points:?\s*/i, '');
                        formatted = formatted.replace(/^Key Details:?\s*/i, '');
                        formatted = formatted.replace(/^Important Information:?\s*/i, '');
                        formatted = formatted.replace(/^Disclosure:?\s*/i, '');
                        formatted = formatted.replace(/^Summary:?\s*/i, '');

                        // Also remove these phrases if they appear anywhere in the text
                        formatted = formatted.replace(/Key Information:?\s*/gi, '');
                        formatted = formatted.replace(/Key Highlights:?\s*/gi, '');
                        formatted = formatted.replace(/Key Points:?\s*/gi, '');

                        // Format dates to be more readable
                        formatted = formatted.replace(/(\d{4}-\d{2}-\d{2})/g, (match) => {
                          const date = new Date(match);
                          return date.toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          });
                        });

                        // Format DD/MM/YYYY dates
                        formatted = formatted.replace(/(\d{2}\/\d{2}\/\d{4})/g, (match) => {
                          const [day, month, year] = match.split('/');
                          const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
                          return date.toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          });
                        });

                        // Clean up extra spaces and punctuation
                        formatted = formatted.replace(/\s+/g, ' ').trim();
                        formatted = formatted.replace(/^[.,;:]\s*/, ''); // Remove leading punctuation

                        // Ensure proper sentence ending
                        if (formatted && !formatted.match(/[.!?]$/)) {
                          formatted += '.';
                        }

                        return formatted;
                      };

                      return (
                        <>
                          {/* Revenue */}
                          {(qrData.revenue || financialMetrics.revenue) && (
                            <div className="flex items-center justify-between p-2 bg-blue-50 rounded-lg">
                              <span className="text-sm font-medium text-blue-700">Revenue</span>
                              <div className="text-right">
                                <div className="text-sm font-semibold text-blue-900">
                                  {isNewFormat ? financialMetrics.revenue.current : qrData.revenue.current_quarter}
                                </div>
                                {(isNewFormat ? financialMetrics.revenue.growth : qrData.revenue.yoy_growth_percentage) && (
                                  <div className={`text-xs flex items-center gap-1 ${(isNewFormat ? financialMetrics.revenue.growth : qrData.revenue.yoy_growth_percentage).startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                                    {(isNewFormat ? financialMetrics.revenue.growth : qrData.revenue.yoy_growth_percentage).startsWith('+') ? '↗' : '↘'}
                                    {isNewFormat ? financialMetrics.revenue.growth : qrData.revenue.yoy_growth_percentage} YoY
                                  </div>
                                )}
                              </div>
                            </div>
                          )}

                          {/* EBITDA */}
                          {(qrData.ebitda || financialMetrics.ebitda) && (
                            <div className="flex items-center justify-between p-2 bg-green-50 rounded-lg">
                              <span className="text-sm font-medium text-green-700">EBITDA</span>
                              <div className="text-right">
                                <div className="text-sm font-semibold text-green-900">
                                  {isNewFormat ? financialMetrics.ebitda.current : qrData.ebitda.current_quarter}
                                </div>
                                {(isNewFormat ? financialMetrics.ebitda.growth : qrData.ebitda.yoy_growth_percentage) && (
                                  <div className={`text-xs flex items-center gap-1 ${(isNewFormat ? financialMetrics.ebitda.growth : qrData.ebitda.yoy_growth_percentage).startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                                    {(isNewFormat ? financialMetrics.ebitda.growth : qrData.ebitda.yoy_growth_percentage).startsWith('+') ? '↗' : '↘'}
                                    {isNewFormat ? financialMetrics.ebitda.growth : qrData.ebitda.yoy_growth_percentage} YoY
                                  </div>
                                )}
                              </div>
                            </div>
                          )}

                          {/* PAT */}
                          {(qrData.pat || financialMetrics.pat) && (
                            <div className="flex items-center justify-between p-2 bg-purple-50 rounded-lg">
                              <span className="text-sm font-medium text-purple-700">PAT</span>
                              <div className="text-right">
                                <div className="text-sm font-semibold text-purple-900">
                                  {isNewFormat ? financialMetrics.pat.current : qrData.pat.current_quarter}
                                </div>
                                {(isNewFormat ? financialMetrics.pat.growth : qrData.pat.yoy_growth_percentage) && (
                                  <div className={`text-xs flex items-center gap-1 ${(isNewFormat ? financialMetrics.pat.growth : qrData.pat.yoy_growth_percentage).startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                                    {(isNewFormat ? financialMetrics.pat.growth : qrData.pat.yoy_growth_percentage).startsWith('+') ? '↗' : '↘'}
                                    {isNewFormat ? financialMetrics.pat.growth : qrData.pat.yoy_growth_percentage} YoY
                                  </div>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Key Highlights */}
                          {((qrData.key_financial_highlights && qrData.key_financial_highlights.length > 0) ||
                            (summaryData.key_disclosures && summaryData.key_disclosures.key_points) ||
                            (summaryData.new_format_data?.key_disclosures && summaryData.new_format_data.key_disclosures.key_points)) && (
                            <div className="mt-3 pt-3 border-t border-slate-100">
                              <span className="text-xs font-medium text-slate-700 block mb-2">Key Highlights:</span>
                              <ul className="space-y-1">
                                {/* Always show key disclosures first if available */}
                                {summaryData.key_disclosures?.key_points && (
                                  <li className="text-xs text-slate-600 flex items-start gap-2">
                                    <span className="w-1 h-1 bg-slate-400 rounded-full mt-1.5 flex-shrink-0"></span>
                                    {formatKeyPoints(summaryData.key_disclosures.key_points)}
                                  </li>
                                )}

                                {/* Show nested key_disclosures if different from main */}
                                {summaryData.new_format_data?.key_disclosures?.key_points &&
                                 summaryData.new_format_data.key_disclosures.key_points !== summaryData.key_disclosures?.key_points && (
                                  <li className="text-xs text-slate-600 flex items-start gap-2">
                                    <span className="w-1 h-1 bg-slate-400 rounded-full mt-1.5 flex-shrink-0"></span>
                                    {formatKeyPoints(summaryData.new_format_data.key_disclosures.key_points)}
                                  </li>
                                )}

                                {/* Show financial highlights for quarterly results */}
                                {qrData.key_financial_highlights?.slice(0, 3).map((highlight: string, idx: number) => (
                                  <li key={idx} className="text-xs text-slate-600 flex items-start gap-2">
                                    <span className="w-1 h-1 bg-slate-400 rounded-full mt-1.5 flex-shrink-0"></span>
                                    {highlight}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </>
                      );
                    })()}
                  </div>
                )}
              </div>
            )}

            {/* Company Information - NEW FORMAT ONLY */}
            {(analysisResult as any).summary?.company_information && (
              <div className="border border-slate-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => toggleSection('company')}
                  className="w-full px-3 py-2 bg-slate-50 hover:bg-slate-100 flex items-center justify-between text-left transition-colors"
                >
                  <span className="text-sm font-medium text-slate-900">Company Information</span>
                  {expandedSections.company ? (
                    <ChevronUp className="w-4 h-4 text-slate-500" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-slate-500" />
                  )}
                </button>
                {expandedSections.company && (
                  <div className="p-3 bg-white">
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="font-medium text-slate-700">Company:</span>
                        <p className="text-slate-900 mt-1">{(analysisResult as any).summary?.company_information?.company_name || 'N/A'}</p>
                      </div>
                      <div>
                        <span className="font-medium text-slate-700">Filing Date:</span>
                        <p className="text-slate-900 mt-1">{(analysisResult as any).summary?.company_information?.filing_date || 'N/A'}</p>
                      </div>
                      {(analysisResult as any).summary?.company_information?.quarter && (
                        <div>
                          <span className="font-medium text-slate-700">Quarter:</span>
                          <p className="text-slate-900 mt-1">{(analysisResult as any).summary.company_information.quarter}</p>
                        </div>
                      )}
                      {(analysisResult as any).summary?.company_information?.stock_exchanges && (
                        <div>
                          <span className="font-medium text-slate-700">Exchanges:</span>
                          <div className="flex gap-1 mt-1">
                            {(analysisResult as any).summary.company_information.stock_exchanges.map((exchange: string, idx: number) => (
                              <span key={idx} className="px-1 py-0.5 bg-blue-100 text-blue-800 text-xs rounded">
                                {exchange}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* AI Summary - NEW FORMAT ONLY */}
            {(analysisResult as any).summary?.summary && (
              <div className="border border-slate-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => toggleSection('summary')}
                  className="w-full px-3 py-2 bg-slate-50 hover:bg-slate-100 flex items-center justify-between text-left transition-colors"
                >
                  <span className="text-sm font-medium text-slate-900">AI Summary</span>
                  {expandedSections.summary ? (
                    <ChevronUp className="w-4 h-4 text-slate-500" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-slate-500" />
                  )}
                </button>
                {expandedSections.summary && (
                  <div className="p-3 bg-white">
                    <p className="text-sm text-slate-700 leading-relaxed">
                      {((analysisResult as any).summary?.summary || '').replace(/not specified/gi, '').replace(/\s+/g, ' ').trim()}
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Regulatory Details - NEW FORMAT ONLY */}
            {(analysisResult as any).summary?.extracted_data?.regulatory_disclosure && (
              <div className="border border-slate-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => toggleSection('regulatory')}
                  className="w-full px-3 py-2 bg-slate-50 hover:bg-slate-100 flex items-center justify-between text-left transition-colors"
                >
                  <span className="text-sm font-medium text-slate-900">Regulatory Details</span>
                  {expandedSections.regulatory ? (
                    <ChevronUp className="w-4 h-4 text-slate-500" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-slate-500" />
                  )}
                </button>
                {expandedSections.regulatory && (
                  <div className="p-3 bg-white space-y-2">
                    {(() => {
                      const regData = (analysisResult as any).summary?.extracted_data?.regulatory_disclosure;
                      return (
                        <>
                          {regData.person_entity && regData.person_entity !== 'Not specified' && (
                            <div className="text-xs">
                              <span className="font-medium text-slate-700">Person/Entity:</span>
                              <span className="ml-2 text-slate-900">{regData.person_entity}</span>
                            </div>
                          )}
                          {regData.transaction_details?.shares && regData.transaction_details.shares !== 'Not specified' && (
                            <div className="text-xs">
                              <span className="font-medium text-slate-700">Shares:</span>
                              <span className="ml-2 text-slate-900">{regData.transaction_details.shares}</span>
                            </div>
                          )}
                          {regData.transaction_details?.transaction_type && regData.transaction_details.transaction_type !== 'Not specified' && (
                            <div className="text-xs">
                              <span className="font-medium text-slate-700">Transaction:</span>
                              <span className="ml-2 text-slate-900">{regData.transaction_details.transaction_type}</span>
                            </div>
                          )}
                          {regData.shareholding_changes?.before_transaction && regData.shareholding_changes?.after_transaction && (
                            <div className="text-xs">
                              <span className="font-medium text-slate-700">Shareholding Change:</span>
                              <div className="ml-2 text-slate-900">
                                <span className="text-red-600">{regData.shareholding_changes.before_transaction}</span>
                                <span className="mx-2">→</span>
                                <span className="text-green-600">{regData.shareholding_changes.after_transaction}</span>
                              </div>
                            </div>
                          )}
                        </>
                      );
                    })()}
                  </div>
                )}
              </div>
            )}

            {/* Investment Implications - NEW FORMAT ONLY */}
            {(analysisResult as any).summary?.investment_implications?.length > 0 && (
              <div className="border border-slate-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => toggleSection('investment')}
                  className="w-full px-3 py-2 bg-slate-50 hover:bg-slate-100 flex items-center justify-between text-left transition-colors"
                >
                  <span className="text-sm font-medium text-slate-900">Investment Implications</span>
                  {expandedSections.investment ? (
                    <ChevronUp className="w-4 h-4 text-slate-500" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-slate-500" />
                  )}
                </button>
                {expandedSections.investment && (
                  <div className="p-3 bg-white">
                    <ul className="space-y-1">
                      {((analysisResult as any).summary?.investment_implications || []).map((implication: string, index: number) => (
                        <li key={index} className="text-sm text-slate-700 flex items-start gap-2">
                          <span className="w-1 h-1 bg-slate-400 rounded-full mt-2 flex-shrink-0"></span>
                          {implication}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
