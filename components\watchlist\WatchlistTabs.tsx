'use client';

import { useState, useRef } from 'react';
import { Plus, Edit2, Trash2, MoreHorizontal } from 'lucide-react';
import { Watchlist } from '@/lib/watchlist/types';
import DropdownPortal from '@/components/ui/DropdownPortal';

interface WatchlistTabsProps {
  watchlists: Watchlist[];
  selectedWatchlistId?: string;
  onWatchlistSelect: (watchlistId: string) => void;
  onCreateWatchlist?: () => void;
  onEditWatchlist?: (watchlist: Watchlist) => void;
  onDeleteWatchlist?: (watchlistId: string) => void;
}

export default function WatchlistTabs({
  watchlists,
  selectedWatchlistId,
  onWatchlistSelect,
  onCreateWatchlist,
  onEditWatchlist,
  onDeleteWatchlist
}: WatchlistTabsProps) {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const buttonRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});

  // Remove the old click outside handler since Dropdown<PERSON>ortal handles this

  const handleDropdownToggle = (watchlistId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setActiveDropdown(activeDropdown === watchlistId ? null : watchlistId);
  };

  const handleEdit = (watchlist: Watchlist, event: React.MouseEvent) => {
    event.stopPropagation();
    setActiveDropdown(null);
    onEditWatchlist?.(watchlist);
  };

  const handleDelete = (watchlistId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setActiveDropdown(null);
    if (confirm('Are you sure you want to delete this watchlist?')) {
      onDeleteWatchlist?.(watchlistId);
    }
  };

  return (
    <div className="border-b border-slate-200">
      {/* Horizontal Scrollable Tabs */}
      <div className="flex items-center space-x-1 overflow-x-auto scrollbar-hide">
        <div className="flex space-x-1 min-w-max px-4 py-2">
          {/* Watchlist Tabs */}
          {watchlists.map((watchlist) => (
            <div key={watchlist.id} className="relative flex-shrink-0">
              <div
                className={`
                  flex items-center space-x-2 px-3 py-2 text-sm font-medium border-b-2 transition-colors cursor-pointer group
                  ${selectedWatchlistId === watchlist.id
                    ? 'border-blue-600 text-blue-600'
                    : 'border-transparent text-slate-500 hover:text-slate-700'
                  }
                `}
                onClick={() => onWatchlistSelect(watchlist.id)}
              >
                <span className="whitespace-nowrap">
                  {watchlist.name}
                  {watchlist.is_default && (
                    <span className="ml-1 text-xs bg-slate-100 text-slate-600 px-1.5 py-0.5 rounded">
                      Default
                    </span>
                  )}
                </span>
                
                {/* Actions Button */}
                <button
                  ref={(el) => { buttonRefs.current[watchlist.id] = el; }}
                  onClick={(e) => handleDropdownToggle(watchlist.id, e)}
                  className="opacity-0 group-hover:opacity-100 p-1 hover:bg-slate-100 rounded transition-opacity"
                >
                  <MoreHorizontal className="w-3 h-3" />
                </button>
              </div>

              {/* Dropdown Menu */}
              <DropdownPortal
                isOpen={activeDropdown === watchlist.id}
                onClose={() => setActiveDropdown(null)}
                triggerRef={{ current: buttonRefs.current[watchlist.id] }}
                placement="bottom-left"
                offset={{ x: 0, y: 4 }}
              >
                <button
                  onClick={(e) => handleEdit(watchlist, e)}
                  className="w-full px-3 py-2 text-left text-sm text-slate-700 hover:bg-slate-50 flex items-center space-x-2"
                >
                  <Edit2 className="w-3 h-3" />
                  <span>Edit</span>
                </button>

                {!watchlist.is_default && (
                  <button
                    onClick={(e) => handleDelete(watchlist.id, e)}
                    className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                  >
                    <Trash2 className="w-3 h-3" />
                    <span>Delete</span>
                  </button>
                )}
              </DropdownPortal>
            </div>
          ))}

          {/* Create New Watchlist Button */}
          {onCreateWatchlist && (
            <button
              onClick={onCreateWatchlist}
              className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors flex-shrink-0"
            >
              <Plus className="w-4 h-4" />
              <span className="whitespace-nowrap">New Watchlist</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
