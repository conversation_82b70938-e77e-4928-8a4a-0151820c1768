import yahooFinance from 'yahoo-finance2';

// Single symbol
const result = await yahooFinance.quote(['ROBU.BO', 'AVANCE.BO']);

// Filter to only show the required fields
const filteredData = result.map(stock => ({
    symbol: stock.symbol,
    currency: stock.currency,
    exchange: stock.exchange,
    regularMarketPrice: stock.regularMarketPrice,
    regularMarketChange: stock.regularMarketChange,
    regularMarketChangePercent: stock.regularMarketChangePercent,
    marketCap: stock.marketCap,
    fiftyTwoWeekLow: stock.fiftyTwoWeekLow,
    fiftyTwoWeekHigh: stock.fiftyTwoWeekHigh
}));

console.log(filteredData);
