'use client';

import React, { useState } from 'react';
import { ChevronDown, ChevronUp, ExternalLink, FileText, Brain, CheckCircle, AlertCircle } from 'lucide-react';
import { DocumentAnalysisResult } from '@/lib/documentAnalysisService';

interface CompleteAnalysisDisplayProps {
  result: DocumentAnalysisResult;
  className?: string;
}

export default function CompleteAnalysisDisplay({ result, className = '' }: CompleteAnalysisDisplayProps) {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    classification: true,
    company: true,
    summary: true,
    extracted: false,
    investment: false
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  if (result.status === 'error') {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center gap-2 text-red-700">
          <AlertCircle className="w-5 h-5" />
          <span className="font-medium">Analysis Failed</span>
        </div>
        <p className="text-red-600 text-sm mt-2">{result.error_message}</p>
      </div>
    );
  }

  const keyDetails = result.key_details || {};
  const classification = (keyDetails.document_classification || {}) as any;
  const companyInfo = (keyDetails.company_information || {}) as any;

  const categories = (keyDetails.categories || []) as string[];
  const primaryCategory = keyDetails.primary_category || 'OTHERS';

  const renderSection = (title: string, sectionKey: string, children: React.ReactNode) => (
    <div className="border border-slate-200 rounded-lg overflow-hidden">
      <button
        onClick={() => toggleSection(sectionKey)}
        className="w-full px-4 py-3 bg-slate-50 hover:bg-slate-100 flex items-center justify-between text-left transition-colors"
      >
        <span className="font-medium text-slate-900">{title}</span>
        {expandedSections[sectionKey] ? (
          <ChevronUp className="w-4 h-4 text-slate-500" />
        ) : (
          <ChevronDown className="w-4 h-4 text-slate-500" />
        )}
      </button>
      {expandedSections[sectionKey] && (
        <div className="p-4 bg-white">
          {children}
        </div>
      )}
    </div>
  );

  const renderCategoryBadge = (category: string, isPrimary: boolean = false) => {
    const categoryColors: Record<string, string> = {
      'EARNINGS_CALL': 'bg-purple-100 text-purple-800',
      'CONTRACT_AWARD': 'bg-green-100 text-green-800',
      'QUARTERLY_RESULTS': 'bg-blue-100 text-blue-800',
      'PRESS_CONFERENCE': 'bg-yellow-100 text-yellow-800',
      'INSIDER_TRADING': 'bg-orange-100 text-orange-800',
      'MA_ANNOUNCEMENT': 'bg-red-100 text-red-800',
      'NEWSPAPER_PUBLICATION': 'bg-gray-100 text-gray-800',
      'OTHERS': 'bg-slate-100 text-slate-800'
    };

    const colorClass = categoryColors[category] || 'bg-slate-100 text-slate-800';
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass} ${isPrimary ? 'ring-2 ring-offset-1 ring-blue-500' : ''}`}>
        {category.replace('_', ' ')}
        {isPrimary && <span className="ml-1 text-blue-600">★</span>}
      </span>
    );
  };

  return (
    <div className={`bg-white border border-slate-200 rounded-xl shadow-sm p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <Brain className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-slate-900">Complete AI Analysis</h2>
            <p className="text-sm text-slate-600">{result.company_name}</p>
          </div>
        </div>
        <div className="flex items-center gap-2 text-sm text-green-600">
          <CheckCircle className="w-4 h-4" />
          <span>Analysis Complete</span>
        </div>
      </div>

      {/* Document Classification */}
      {renderSection('Document Classification', 'classification', (
        <div className="space-y-3">
          <div>
            <span className="text-sm font-medium text-slate-700 block mb-2">Categories:</span>
            <div className="flex flex-wrap gap-2">
              {Array.isArray(categories) && categories.map((category: string) =>
                renderCategoryBadge(category, category === primaryCategory)
              )}
            </div>
          </div>
          {classification.confidence_scores && (
            <div>
              <span className="text-sm font-medium text-slate-700 block mb-2">Confidence Scores:</span>
              <div className="space-y-1">
                {Object.entries(classification.confidence_scores).map(([category, score]) => (
                  <div key={category} className="flex items-center justify-between text-sm">
                    <span className="text-slate-600">{category.replace('_', ' ')}</span>
                    <span className="font-medium">{Math.round((score as number) * 100)}%</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ))}

      {/* Company Information */}
      {renderSection('Company Information', 'company', (
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-sm font-medium text-slate-700">Company:</span>
              <p className="text-sm text-slate-900 mt-1">{companyInfo.company_name || 'N/A'}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-slate-700">Filing Date:</span>
              <p className="text-sm text-slate-900 mt-1">{companyInfo.filing_date || 'N/A'}</p>
            </div>
          </div>
          {companyInfo.stock_exchanges && (
            <div>
              <span className="text-sm font-medium text-slate-700">Stock Exchanges:</span>
              <div className="flex gap-2 mt-1">
                {companyInfo.stock_exchanges.map((exchange: string) => (
                  <span key={exchange} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                    {exchange}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      ))}

      {/* Summary */}
      {renderSection('AI Summary', 'summary', (
        <div className="prose prose-sm max-w-none">
          <p className="text-slate-700 leading-relaxed">{result.summary}</p>
        </div>
      ))}

      {/* View PDF Button */}
      <div className="mt-6 pt-4 border-t border-slate-100">
        <button
          onClick={() => window.open(result.source_url, '_blank')}
          className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <FileText className="w-4 h-4" />
          <span>View Original Document</span>
          <ExternalLink className="w-3 h-3" />
        </button>
      </div>
    </div>
  );
}
