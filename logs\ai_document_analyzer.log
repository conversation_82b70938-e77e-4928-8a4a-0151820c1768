{"timestamp": "2025-07-31T03:54:14.109700", "level": "INFO", "logger": "src.config.config_manager", "message": "Loaded environment variables from C:\\Users\\<USER>\\Desktop\\strikedeck-test\\ai_summarizer\\.env", "module": "config_manager", "function": "_load_environment", "line": 52}
{"timestamp": "2025-07-31T03:54:16.339164", "level": "INFO", "logger": "src.config.config_manager", "message": "Successfully loaded configuration: settings", "module": "config_manager", "function": "load_config", "line": 96}
{"timestamp": "2025-07-31T03:54:16.341088", "level": "INFO", "logger": "src.core.prompt_manager", "message": "Prompt manager initialized with directory: C:\\Users\\<USER>\\Desktop\\strikedeck-test\\ai_summarizer\\prompts", "module": "prompt_manager", "function": "__init__", "line": 83}
{"timestamp": "2025-07-31T03:54:18.008712", "level": "INFO", "logger": "src.core.classification_engine", "message": "Classification engine initialized with LangChain", "module": "classification_engine", "function": "__init__", "line": 87}
{"timestamp": "2025-07-31T03:54:18.099385", "level": "INFO", "logger": "src.core.extraction_engine", "message": "Data extraction engine initialized", "module": "extraction_engine", "function": "__init__", "line": 84}
{"timestamp": "2025-07-31T03:54:18.128221", "level": "INFO", "logger": "src.core.output_manager", "message": "Output manager initialized with directory: C:\\Users\\<USER>\\Desktop\\strikedeck-test\\output", "module": "output_manager", "function": "__init__", "line": 53}
{"timestamp": "2025-07-31T03:54:18.131616", "level": "INFO", "logger": "src.core.tag_extractor", "message": "Tag extractor initialized", "module": "tag_extractor", "function": "__init__", "line": 37}
{"timestamp": "2025-07-31T03:54:18.133686", "level": "INFO", "logger": "src.core.json_optimizer", "message": "JSON optimizer initialized", "module": "json_optimizer", "function": "__init__", "line": 48}
{"timestamp": "2025-07-31T03:54:18.135480", "level": "INFO", "logger": "__main__", "message": "Starting document analysis: https://invalid-url-test.com/nonexistent.pdf", "module": "integrated_analyzer", "function": "analyze_document_legacy", "line": 159}
{"timestamp": "2025-07-31T03:54:18.137156", "level": "INFO", "logger": "src.core.document_processor", "message": "Document processor initialized", "module": "document_processor", "function": "__init__", "line": 82}
{"timestamp": "2025-07-31T03:54:18.138060", "level": "INFO", "logger": "src.core.document_processor", "message": "Processing document from url: https://invalid-url-test.com/nonexistent.pdf", "module": "document_processor", "function": "process_document", "line": 109}
{"timestamp": "2025-07-31T03:54:18.449852", "level": "ERROR", "logger": "error", "message": "Error: Failed to download PDF: HTTPSConnectionPool(host='invalid-url-test.com', port=443): Max retries exceeded with url: /nonexistent.pdf (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001849D5034D0>: Failed to resolve 'invalid-url-test.com' ([Errno 11001] getaddrinfo failed)\"))", "module": "logger", "function": "log_error", "line": 275, "processing_time": 0.032922983169555664, "error_code": "DOC_001", "exception": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\urllib3\\connection.py\", line 198, in _new_conn\n    sock = connection.create_connection(\n        (self._dns_host, self.port),\n    ...<2 lines>...\n        socket_options=self.socket_options,\n    )\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\urllib3\\util\\connection.py\", line 60, in create_connection\n    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):\n               ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Python313\\Lib\\socket.py\", line 977, in getaddrinfo\n    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):\n               ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nsocket.gaierror: [Errno 11001] getaddrinfo failed\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\urllib3\\connectionpool.py\", line 787, in urlopen\n    response = self._make_request(\n        conn,\n    ...<10 lines>...\n        **response_kw,\n    )\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\urllib3\\connectionpool.py\", line 488, in _make_request\n    raise new_e\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\urllib3\\connectionpool.py\", line 464, in _make_request\n    self._validate_conn(conn)\n    ~~~~~~~~~~~~~~~~~~~^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\urllib3\\connectionpool.py\", line 1093, in _validate_conn\n    conn.connect()\n    ~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\urllib3\\connection.py\", line 753, in connect\n    self.sock = sock = self._new_conn()\n                       ~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\urllib3\\connection.py\", line 205, in _new_conn\n    raise NameResolutionError(self.host, self, e) from e\nurllib3.exceptions.NameResolutionError: <urllib3.connection.HTTPSConnection object at 0x000001849D5034D0>: Failed to resolve 'invalid-url-test.com' ([Errno 11001] getaddrinfo failed)\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\adapters.py\", line 667, in send\n    resp = conn.urlopen(\n        method=request.method,\n    ...<9 lines>...\n        chunked=chunked,\n    )\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\urllib3\\connectionpool.py\", line 841, in urlopen\n    retries = retries.increment(\n        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]\n    )\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\urllib3\\util\\retry.py\", line 519, in increment\n    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nurllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='invalid-url-test.com', port=443): Max retries exceeded with url: /nonexistent.pdf (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001849D5034D0>: Failed to resolve 'invalid-url-test.com' ([Errno 11001] getaddrinfo failed)\"))\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\strikedeck-test\\ai_summarizer\\src\\core\\document_processor.py\", line 226, in _download_pdf\n    response = requests.get(\n        url,\n    ...<3 lines>...\n        allow_redirects=True\n    )\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\api.py\", line 73, in get\n    return request(\"get\", url, params=params, **kwargs)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\api.py\", line 59, in request\n    return session.request(method=method, url=url, **kwargs)\n           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\sessions.py\", line 589, in request\n    resp = self.send(prep, **send_kwargs)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\sessions.py\", line 703, in send\n    r = adapter.send(request, **kwargs)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\adapters.py\", line 700, in send\n    raise ConnectionError(e, request=request)\nrequests.exceptions.ConnectionError: HTTPSConnectionPool(host='invalid-url-test.com', port=443): Max retries exceeded with url: /nonexistent.pdf (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001849D5034D0>: Failed to resolve 'invalid-url-test.com' ([Errno 11001] getaddrinfo failed)\"))\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\strikedeck-test\\ai_summarizer\\src\\core\\document_processor.py\", line 113, in process_document\n    pdf_content = self._download_pdf(str(source))\n  File \"C:\\Users\\<USER>\\Desktop\\strikedeck-test\\ai_summarizer\\src\\core\\document_processor.py\", line 260, in _download_pdf\n    raise DocumentProcessingError(\n    ...<3 lines>...\n    )\nsrc.utils.exceptions.DocumentProcessingError: Failed to download PDF: HTTPSConnectionPool(host='invalid-url-test.com', port=443): Max retries exceeded with url: /nonexistent.pdf (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001849D5034D0>: Failed to resolve 'invalid-url-test.com' ([Errno 11001] getaddrinfo failed)\"))"}
{"timestamp": "2025-07-31T03:54:19.475135", "level": "ERROR", "logger": "__main__", "message": "Document analysis failed: Failed to download PDF: HTTPSConnectionPool(host='invalid-url-test.com', port=443): Max retries exceeded with url: /nonexistent.pdf (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001849D5034D0>: Failed to resolve 'invalid-url-test.com' ([Errno 11001] getaddrinfo failed)\"))", "module": "integrated_analyzer", "function": "analyze_document_legacy", "line": 227}
{"timestamp": "2025-07-31T03:56:44.233490", "level": "INFO", "logger": "src.config.config_manager", "message": "Loaded environment variables from C:\\Users\\<USER>\\Desktop\\strikedeck-test\\ai_summarizer\\.env", "module": "config_manager", "function": "_load_environment", "line": 52}
{"timestamp": "2025-07-31T03:56:46.004354", "level": "INFO", "logger": "src.config.config_manager", "message": "Successfully loaded configuration: settings", "module": "config_manager", "function": "load_config", "line": 96}
{"timestamp": "2025-07-31T03:56:46.006503", "level": "INFO", "logger": "src.core.prompt_manager", "message": "Prompt manager initialized with directory: C:\\Users\\<USER>\\Desktop\\strikedeck-test\\ai_summarizer\\prompts", "module": "prompt_manager", "function": "__init__", "line": 83}
{"timestamp": "2025-07-31T03:56:47.786411", "level": "INFO", "logger": "src.core.classification_engine", "message": "Classification engine initialized with LangChain", "module": "classification_engine", "function": "__init__", "line": 87}
{"timestamp": "2025-07-31T03:56:47.954965", "level": "INFO", "logger": "src.core.extraction_engine", "message": "Data extraction engine initialized", "module": "extraction_engine", "function": "__init__", "line": 84}
{"timestamp": "2025-07-31T03:56:47.988242", "level": "INFO", "logger": "src.core.output_manager", "message": "Output manager initialized with directory: C:\\Users\\<USER>\\Desktop\\strikedeck-test\\output", "module": "output_manager", "function": "__init__", "line": 53}
{"timestamp": "2025-07-31T03:56:47.993248", "level": "INFO", "logger": "src.core.tag_extractor", "message": "Tag extractor initialized", "module": "tag_extractor", "function": "__init__", "line": 37}
{"timestamp": "2025-07-31T03:56:47.995774", "level": "INFO", "logger": "src.core.json_optimizer", "message": "JSON optimizer initialized", "module": "json_optimizer", "function": "__init__", "line": 48}
{"timestamp": "2025-07-31T03:59:56.505962", "level": "INFO", "logger": "src.config.config_manager", "message": "Loaded environment variables from C:\\Users\\<USER>\\Desktop\\strikedeck-test\\ai_summarizer\\.env", "module": "config_manager", "function": "_load_environment", "line": 52}
{"timestamp": "2025-07-31T03:59:58.367064", "level": "INFO", "logger": "src.config.config_manager", "message": "Successfully loaded configuration: settings", "module": "config_manager", "function": "load_config", "line": 96}
{"timestamp": "2025-07-31T03:59:58.370503", "level": "INFO", "logger": "src.core.prompt_manager", "message": "Prompt manager initialized with directory: C:\\Users\\<USER>\\Desktop\\strikedeck-test\\ai_summarizer\\prompts", "module": "prompt_manager", "function": "__init__", "line": 83}
{"timestamp": "2025-07-31T03:59:59.835805", "level": "INFO", "logger": "src.core.classification_engine", "message": "Classification engine initialized with LangChain", "module": "classification_engine", "function": "__init__", "line": 87}
{"timestamp": "2025-07-31T03:59:59.931541", "level": "INFO", "logger": "src.core.extraction_engine", "message": "Data extraction engine initialized", "module": "extraction_engine", "function": "__init__", "line": 84}
{"timestamp": "2025-07-31T03:59:59.960511", "level": "INFO", "logger": "src.core.output_manager", "message": "Output manager initialized with directory: C:\\Users\\<USER>\\Desktop\\strikedeck-test\\output", "module": "output_manager", "function": "__init__", "line": 53}
{"timestamp": "2025-07-31T03:59:59.963956", "level": "INFO", "logger": "src.core.tag_extractor", "message": "Tag extractor initialized", "module": "tag_extractor", "function": "__init__", "line": 37}
{"timestamp": "2025-07-31T03:59:59.965910", "level": "INFO", "logger": "src.core.json_optimizer", "message": "JSON optimizer initialized", "module": "json_optimizer", "function": "__init__", "line": 48}
{"timestamp": "2025-07-31T04:00:50.707131", "level": "INFO", "logger": "src.config.config_manager", "message": "Loaded environment variables from C:\\Users\\<USER>\\Desktop\\strikedeck-test\\ai_summarizer\\.env", "module": "config_manager", "function": "_load_environment", "line": 52}
{"timestamp": "2025-07-31T04:00:53.281782", "level": "INFO", "logger": "src.config.config_manager", "message": "Successfully loaded configuration: settings", "module": "config_manager", "function": "load_config", "line": 96}
{"timestamp": "2025-07-31T04:00:53.286950", "level": "INFO", "logger": "src.core.prompt_manager", "message": "Prompt manager initialized with directory: C:\\Users\\<USER>\\Desktop\\strikedeck-test\\ai_summarizer\\prompts", "module": "prompt_manager", "function": "__init__", "line": 83}
{"timestamp": "2025-07-31T04:00:55.451428", "level": "INFO", "logger": "src.core.classification_engine", "message": "Classification engine initialized with LangChain", "module": "classification_engine", "function": "__init__", "line": 87}
{"timestamp": "2025-07-31T04:00:55.541338", "level": "INFO", "logger": "src.core.extraction_engine", "message": "Data extraction engine initialized", "module": "extraction_engine", "function": "__init__", "line": 84}
{"timestamp": "2025-07-31T04:00:55.574512", "level": "INFO", "logger": "src.core.output_manager", "message": "Output manager initialized with directory: C:\\Users\\<USER>\\Desktop\\strikedeck-test\\output", "module": "output_manager", "function": "__init__", "line": 53}
{"timestamp": "2025-07-31T04:00:55.578251", "level": "INFO", "logger": "src.core.tag_extractor", "message": "Tag extractor initialized", "module": "tag_extractor", "function": "__init__", "line": 37}
{"timestamp": "2025-07-31T04:00:55.580275", "level": "INFO", "logger": "src.core.json_optimizer", "message": "JSON optimizer initialized", "module": "json_optimizer", "function": "__init__", "line": 48}
{"timestamp": "2025-07-31T04:01:34.648705", "level": "INFO", "logger": "src.config.config_manager", "message": "Loaded environment variables from C:\\Users\\<USER>\\Desktop\\strikedeck-test\\ai_summarizer\\.env", "module": "config_manager", "function": "_load_environment", "line": 52}
{"timestamp": "2025-07-31T04:01:36.684152", "level": "INFO", "logger": "src.config.config_manager", "message": "Successfully loaded configuration: settings", "module": "config_manager", "function": "load_config", "line": 96}
{"timestamp": "2025-07-31T04:01:36.686835", "level": "INFO", "logger": "src.core.prompt_manager", "message": "Prompt manager initialized with directory: C:\\Users\\<USER>\\Desktop\\strikedeck-test\\ai_summarizer\\prompts", "module": "prompt_manager", "function": "__init__", "line": 83}
{"timestamp": "2025-07-31T04:01:38.218418", "level": "INFO", "logger": "src.core.classification_engine", "message": "Classification engine initialized with LangChain", "module": "classification_engine", "function": "__init__", "line": 87}
{"timestamp": "2025-07-31T04:01:38.317947", "level": "INFO", "logger": "src.core.extraction_engine", "message": "Data extraction engine initialized", "module": "extraction_engine", "function": "__init__", "line": 84}
{"timestamp": "2025-07-31T04:01:38.350935", "level": "INFO", "logger": "src.core.output_manager", "message": "Output manager initialized with directory: C:\\Users\\<USER>\\Desktop\\strikedeck-test\\output", "module": "output_manager", "function": "__init__", "line": 53}
{"timestamp": "2025-07-31T04:01:38.354918", "level": "INFO", "logger": "src.core.tag_extractor", "message": "Tag extractor initialized", "module": "tag_extractor", "function": "__init__", "line": 37}
{"timestamp": "2025-07-31T04:01:38.356452", "level": "INFO", "logger": "src.core.json_optimizer", "message": "JSON optimizer initialized", "module": "json_optimizer", "function": "__init__", "line": 48}
