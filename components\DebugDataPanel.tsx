'use client';

import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Bug } from 'lucide-react';

interface DebugDataPanelProps {
  data: any;
  title?: string;
  className?: string;
}

export default function DebugDataPanel({ 
  data, 
  title = "Debug Data", 
  className = '' 
}: DebugDataPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!data) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-3 ${className}`}>
        <div className="flex items-center gap-2 text-red-700">
          <Bug className="w-4 h-4" />
          <span className="text-sm font-medium">{title}: No Data</span>
        </div>
      </div>
    );
  }

  const hasAGMEGMDetails = data.agm_egm_details && Object.keys(data.agm_egm_details).length > 0;
  const documentType = data.type;
  const tags = data.tags || [];

  return (
    <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-3 ${className}`}>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full text-left"
      >
        <div className="flex items-center gap-2 text-yellow-800">
          <Bug className="w-4 h-4" />
          <span className="text-sm font-medium">{title}</span>
        </div>
        {isExpanded ? (
          <ChevronUp className="w-4 h-4 text-yellow-600" />
        ) : (
          <ChevronDown className="w-4 h-4 text-yellow-600" />
        )}
      </button>

      {isExpanded && (
        <div className="mt-3 space-y-2 text-sm">
          {/* Quick Summary */}
          <div className="bg-white rounded p-2 border">
            <div className="font-medium text-gray-900 mb-2">Quick Summary:</div>
            <div className="space-y-1 text-xs">
              <div>
                <span className="font-medium">Document Type:</span> 
                <span className={`ml-2 px-2 py-1 rounded ${
                  documentType === 'agm_egm' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {documentType || 'undefined'}
                </span>
              </div>
              <div>
                <span className="font-medium">Tags:</span> 
                <span className="ml-2">{tags.length > 0 ? tags.join(', ') : 'None'}</span>
              </div>
              <div>
                <span className="font-medium">Has AGM/EGM Details:</span> 
                <span className={`ml-2 px-2 py-1 rounded ${
                  hasAGMEGMDetails ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {hasAGMEGMDetails ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
          </div>

          {/* AGM/EGM Details */}
          {hasAGMEGMDetails && (
            <div className="bg-white rounded p-2 border">
              <div className="font-medium text-gray-900 mb-2">AGM/EGM Details:</div>
              <pre className="text-xs bg-gray-50 p-2 rounded overflow-x-auto">
                {JSON.stringify(data.agm_egm_details, null, 2)}
              </pre>
            </div>
          )}

          {/* Key Events */}
          {data.key_events && (
            <div className="bg-white rounded p-2 border">
              <div className="font-medium text-gray-900 mb-2">Key Events:</div>
              <pre className="text-xs bg-gray-50 p-2 rounded overflow-x-auto">
                {JSON.stringify(data.key_events, null, 2)}
              </pre>
            </div>
          )}

          {/* Call Details */}
          {data.call_details && (
            <div className="bg-white rounded p-2 border">
              <div className="font-medium text-gray-900 mb-2">Call Details:</div>
              <pre className="text-xs bg-gray-50 p-2 rounded overflow-x-auto">
                {JSON.stringify(data.call_details, null, 2)}
              </pre>
            </div>
          )}

          {/* Key Disclosures */}
          {data.key_disclosures && (
            <div className="bg-white rounded p-2 border">
              <div className="font-medium text-gray-900 mb-2">Key Disclosures:</div>
              <pre className="text-xs bg-gray-50 p-2 rounded overflow-x-auto">
                {JSON.stringify(data.key_disclosures, null, 2)}
              </pre>
            </div>
          )}

          {/* Full Data */}
          <div className="bg-white rounded p-2 border">
            <div className="font-medium text-gray-900 mb-2">Full Data Structure:</div>
            <pre className="text-xs bg-gray-50 p-2 rounded overflow-x-auto max-h-40">
              {JSON.stringify(data, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}
