This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Corporate Filings Analysis Platform

This platform provides AI-powered analysis of corporate filings and announcements using the new **AI Summarizer** module.

### Architecture

- **Frontend**: Next.js with TypeScript and Tailwind CSS
- **Backend**: AI Summarizer module with modular architecture
- **Database**: Supabase for corporate announcements storage
- **AI Processing**: OpenAI GPT models for document analysis

## Getting Started

### Frontend Development

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

### AI Summarizer Setup

The AI document analysis is powered by the `ai_summarizer/` module. To set it up:

1. **Install Python dependencies**:
   ```bash
   cd ai_summarizer
   pip install -r requirements.txt
   ```

2. **Set environment variables**:
   ```bash
   # Copy and configure environment variables
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Test the analyzer**:
   ```bash
   # Test with a single document
   python main.py https://example.com/document.pdf

   # Process Supabase records
   python main_supabase.py batch --limit 5
   ```

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Migration from Enhanced Document Analyzer

This project has been migrated from the `enhanced_document_analyzer/` module to the new `ai_summarizer/` module. Key changes:

### What Changed
- **Modular Architecture**: New system uses separate engines for classification, extraction, and processing
- **Improved Output Format**: Structured schema-based extraction with better financial metrics handling
- **Enhanced Database Integration**: REST API-based Supabase client with better error handling
- **Backward Compatibility**: Bridge script maintains API compatibility for existing frontend

### Migration Benefits
- Better separation of concerns
- More robust error handling
- Schema-validated output formats
- Improved financial data extraction
- Enhanced logging and monitoring

### API Compatibility
The `/api/analyze-document` endpoint continues to work with the same interface, now powered by the new AI Summarizer module.

## Learn More

To learn more about the technologies used:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [AI Summarizer Documentation](./ai_summarizer/README.md) - learn about the document analysis system.
- [Supabase Documentation](https://supabase.com/docs) - learn about the database platform.

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
