'use client';

import React from 'react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface FinancialMetric {
  current: string | number | null;
  previous: string | number | null;
  growth: string | number | null;
}

interface FinancialMetricsProps {
  metrics?: {
    revenue?: FinancialMetric;
    ebitda?: FinancialMetric;
    pat?: FinancialMetric;
    other_metrics?: Record<string, string>;
  };
  className?: string;
}

export default function FinancialMetrics({ metrics, className = '' }: FinancialMetricsProps) {
  if (!metrics || (!metrics.revenue && !metrics.ebitda && !metrics.pat && !metrics.other_metrics)) {
    return null;
  }

  // Format currency values to ensure proper display with Rs./Cr.
  const formatCurrency = (value: string | number | null | undefined): string => {
    // Handle null/undefined
    if (value === null || value === undefined) return 'N/A';

    // Handle numeric values
    if (typeof value === 'number') {
      if (isNaN(value)) return 'N/A';
      return `Rs. ${value} Cr`;
    }

    // Handle string values
    if (typeof value !== 'string') return 'N/A';

    const trimmedValue = value.trim();
    if (!trimmedValue) return 'N/A';

    // If already has proper currency formatting, return as is
    if (trimmedValue.toLowerCase().includes('rs.') && trimmedValue.toLowerCase().includes('cr')) {
      return trimmedValue;
    }

    // If already has Rs. but no Cr, add Cr
    if (trimmedValue.toLowerCase().includes('rs.') && !trimmedValue.toLowerCase().includes('cr')) {
      return `${trimmedValue} Cr`;
    }

    // If has Cr but no Rs., add Rs.
    if (trimmedValue.toLowerCase().includes('cr') && !trimmedValue.toLowerCase().includes('rs.')) {
      return `Rs. ${trimmedValue}`;
    }

    // If has ₹ symbol, convert to Rs. format
    if (trimmedValue.includes('₹')) {
      const cleanValue = trimmedValue.replace('₹', '').trim();
      return `Rs. ${cleanValue} Cr`;
    }

    // Check if it's a pure numeric value (with or without commas/decimals)
    const numericMatch = trimmedValue.match(/^[\d,]+\.?\d*$/);
    if (numericMatch) {
      return `Rs. ${trimmedValue} Cr`;
    }

    // Check if it starts with a number but has other text (like "100 crores")
    if (/^\d/.test(trimmedValue)) {
      // If it contains "crore" or "cr" already, just add Rs.
      if (trimmedValue.toLowerCase().includes('crore') || /\bcr\b/i.test(trimmedValue)) {
        return `Rs. ${trimmedValue}`;
      }
      // Otherwise add both Rs. and Cr
      return `Rs. ${trimmedValue} Cr`;
    }

    return trimmedValue;
  };

  const formatGrowth = (growth: string | number | null | undefined) => {
    // Handle null, undefined, or empty values
    if (growth === null || growth === undefined) return null;

    let numericGrowth = 0;
    let displayValue = '';

    // Handle numeric values
    if (typeof growth === 'number') {
      if (isNaN(growth)) return null;
      numericGrowth = growth;
      displayValue = `${growth > 0 ? '+' : ''}${growth}%`;
    }
    // Handle string values
    else if (typeof growth === 'string') {
      const trimmedGrowth = growth.trim();
      if (!trimmedGrowth) return null;

      // Safely extract numeric value with try-catch
      try {
        numericGrowth = parseFloat(trimmedGrowth.replace(/[^\d.-]/g, ''));
      } catch (error) {
        console.warn('Error parsing growth value:', growth, error);
        return null;
      }

      // Handle NaN case
      if (isNaN(numericGrowth)) return null;

      // Ensure consistent % symbol display for string values
      let cleanValue = trimmedGrowth;

      // Remove existing % if present
      cleanValue = cleanValue.replace(/%/g, '');

      // Add + sign if positive and not already present
      if (numericGrowth > 0 && !cleanValue.includes('+')) {
        cleanValue = '+' + cleanValue.trim();
      }

      // Always add % symbol
      displayValue = `${cleanValue}%`;
    } else {
      return null;
    }

    const isPositive = numericGrowth > 0;
    const isNegative = numericGrowth < 0;

    return {
      value: displayValue,
      isPositive,
      isNegative,
      icon: isPositive ? TrendingUp : isNegative ? TrendingDown : Minus
    };
  };

  const MetricRow = ({
    label,
    metric
  }: {
    label: string;
    metric: FinancialMetric;
  }) => {
    const growth = formatGrowth(metric.growth);

    return (
      <div className="flex py-2 border-b border-gray-100 last:border-b-0">
        <div className="w-20 flex-shrink-0">
          <div className="text-sm font-medium text-gray-900">{label}</div>
        </div>
        <div className="flex-1"></div>
        <div className="w-48 flex flex-col items-end">
          <div className="flex items-center gap-3">
            <div className="text-sm font-semibold text-gray-900">
              {formatCurrency(metric.current)}
            </div>
            {growth && (
              <div className="flex items-center gap-1">
                <growth.icon
                  className={`w-3 h-3 ${
                    growth.isPositive ? 'text-green-600' :
                    growth.isNegative ? 'text-red-600' :
                    'text-gray-500'
                  }`}
                />
                <span
                  className={`text-sm font-medium ${
                    growth.isPositive ? 'text-green-600' :
                    growth.isNegative ? 'text-red-600' :
                    'text-gray-500'
                  }`}
                >
                  {growth.value}
                </span>
              </div>
            )}
          </div>
          <div className="mt-1">
            <div className="text-xs text-gray-500">
              Prev: {formatCurrency(metric.previous)}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={className}>
      {/* Single Consolidated Financial Metrics Box */}
      {(metrics.revenue || metrics.ebitda || metrics.pat) && (
        <div className="bg-white border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-900 mb-3 border-b border-blue-100 pb-2">
            Financial Performance
          </h4>
          <div className="space-y-0">
            {metrics.revenue && (
              <MetricRow label="Revenue" metric={metrics.revenue} />
            )}
            {metrics.ebitda && (
              <MetricRow label="EBITDA" metric={metrics.ebitda} />
            )}
            {metrics.pat && (
              <MetricRow label="PAT" metric={metrics.pat} />
            )}
          </div>
        </div>
      )}

      {/* Other Metrics - Also in single box if present */}
      {metrics.other_metrics && Object.keys(metrics.other_metrics).length > 0 && (
        <div className="bg-white border border-blue-200 rounded-lg p-4 mt-3">
          <h4 className="text-sm font-medium text-blue-900 mb-3 border-b border-blue-100 pb-2">
            Other Metrics
          </h4>
          <div className="grid grid-cols-2 gap-3">
            {Object.entries(metrics.other_metrics).map(([key, value]) => (
              <div key={key} className="text-center">
                <div className="text-xs font-medium text-gray-600 capitalize mb-1">
                  {key.replace(/_/g, ' ')}
                </div>
                <div className="text-sm font-semibold text-gray-900">
                  {value}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
