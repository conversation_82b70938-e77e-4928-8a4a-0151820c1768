'use client';

import React from 'react';
import { Phone, Link, Clock, Info, Copy } from 'lucide-react';
import { CallDetails as CallDetailsType } from '@/lib/types';

interface CallDetailsProps {
  callDetails: CallDetailsType;
  className?: string;
}

export default function CallDetails({ callDetails, className = '' }: CallDetailsProps) {
  // Helper function to check if a value is meaningful
  const hasValue = (value: unknown): boolean => {
    if (!value) return false;
    if (typeof value === 'string') return value.trim().length > 0;
    if (Array.isArray(value)) return value.length > 0 && value.some(item => hasValue(item));
    return true;
  };

  // Check if any call details are available
  const hasCallDetails = callDetails && (
    hasValue(callDetails.registration_url) ||
    hasValue(callDetails.call_timing) ||
    hasValue(callDetails.access_codes) ||
    hasValue(callDetails.special_instructions)
  );

  if (!hasCallDetails) {
    return null;
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className={`bg-white border border-blue-200 rounded-lg p-4 ${className}`}>
      <h4 className="text-sm font-medium text-blue-900 mb-3 border-b border-blue-100 pb-2 flex items-center gap-2">
        <Phone className="w-4 h-4" />
        Conference Call Details
      </h4>
      
      <div className="space-y-3">
        {/* Call Timing */}
        {hasValue(callDetails.call_timing) && (
          <div className="flex items-start gap-3">
            <Clock className="w-4 h-4 text-gray-500 mt-0.5 flex-shrink-0" />
            <div>
              <div className="text-sm font-medium text-gray-900">Call Schedule</div>
              <div className="text-sm text-gray-600">{callDetails.call_timing}</div>
            </div>
          </div>
        )}

        {/* Registration URL */}
        {hasValue(callDetails.registration_url) && (
          <div className="flex items-start gap-3">
            <Link className="w-4 h-4 text-gray-500 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-900">Pre-Registration</div>
              <div className="flex items-center gap-2 mt-1">
                <a 
                  href={callDetails.registration_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-blue-600 hover:text-blue-800 underline break-all"
                >
                  {callDetails.registration_url}
                </a>
                <button
                  onClick={() => copyToClipboard(callDetails.registration_url!)}
                  className="p-1 hover:bg-gray-100 rounded"
                  title="Copy URL"
                >
                  <Copy className="w-3 h-3 text-gray-500" />
                </button>
              </div>
            </div>
          </div>
        )}



        {/* Access Codes */}
        {hasValue(callDetails.access_codes) && (
          <div className="flex items-start gap-3">
            <Info className="w-4 h-4 text-gray-500 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-900">Access Codes</div>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-sm text-gray-600 font-mono bg-gray-50 px-2 py-1 rounded">
                  {callDetails.access_codes}
                </span>
                <button
                  onClick={() => copyToClipboard(callDetails.access_codes!)}
                  className="p-1 hover:bg-gray-100 rounded"
                  title="Copy access code"
                >
                  <Copy className="w-3 h-3 text-gray-500" />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Special Instructions */}
        {hasValue(callDetails.special_instructions) && (
          <div className="flex items-start gap-3">
            <Info className="w-4 h-4 text-gray-500 mt-0.5 flex-shrink-0" />
            <div>
              <div className="text-sm font-medium text-gray-900">Special Instructions</div>
              <div className="text-sm text-gray-600 mt-1">{callDetails.special_instructions}</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
