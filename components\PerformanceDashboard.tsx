'use client';

import { useState, useEffect } from 'react';

interface PerformanceMetrics {
  fcp?: number;
  lcp?: number;
  cls?: number;
  inp?: number;
  ttfb?: number;
  loadTime?: number;
  domContentLoaded?: number;
  resourceCount?: number;
  transferSize?: number;
}

export default function PerformanceDashboard() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV !== 'development') return;

    // Collect navigation timing
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigation) {
      setMetrics(prev => ({
        ...prev,
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        ttfb: navigation.responseStart - navigation.requestStart,
      }));
    }

    // Collect resource timing
    const resources = performance.getEntriesByType('resource');
    const totalSize = resources.reduce((total, resource: any) => 
      total + (resource.transferSize || 0), 0);
    
    setMetrics(prev => ({
      ...prev,
      resourceCount: resources.length,
      transferSize: totalSize,
    }));

    // Collect paint timing
    const paintEntries = performance.getEntriesByType('paint');
    const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    if (fcp) {
      setMetrics(prev => ({ ...prev, fcp: fcp.startTime }));
    }

    // Listen for Web Vitals (if available)
    const handleWebVital = (metric: any) => {
      setMetrics(prev => ({
        ...prev,
        [metric.name.toLowerCase()]: metric.value
      }));
    };

    // Try to get Web Vitals
    if (typeof window !== 'undefined' && 'webVitals' in window) {
      const webVitals = (window as any).webVitals;
      webVitals.onCLS?.(handleWebVital);
      webVitals.onLCP?.(handleWebVital);
      webVitals.onFCP?.(handleWebVital);
      webVitals.onINP?.(handleWebVital);
      webVitals.onTTFB?.(handleWebVital);
    }
  }, []);

  // Don't render in production
  if (process.env.NODE_ENV !== 'development') return null;

  const formatMs = (ms?: number) => ms ? `${ms.toFixed(2)}ms` : 'N/A';
  const formatKB = (bytes?: number) => bytes ? `${(bytes / 1024).toFixed(2)}KB` : 'N/A';
  const formatScore = (value?: number, thresholds?: [number, number]) => {
    if (!value || !thresholds) return 'N/A';
    const [good, poor] = thresholds;
    const color = value <= good ? 'text-green-600' : value <= poor ? 'text-yellow-600' : 'text-red-600';
    return <span className={color}>{formatMs(value)}</span>;
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-blue-600 text-white px-3 py-2 rounded-lg shadow-lg hover:bg-blue-700 transition-colors"
      >
        📊 Performance
      </button>
      
      {isVisible && (
        <div className="absolute bottom-12 right-0 bg-white border border-gray-200 rounded-lg shadow-xl p-4 w-80 max-h-96 overflow-y-auto">
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-bold text-gray-900">Performance Metrics</h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
          
          <div className="space-y-2 text-sm">
            <div className="border-b pb-2">
              <h4 className="font-semibold text-gray-700 mb-1">Core Web Vitals</h4>
              <div className="grid grid-cols-2 gap-2">
                <div>FCP: {formatScore(metrics.fcp, [1800, 3000])}</div>
                <div>LCP: {formatScore(metrics.lcp, [2500, 4000])}</div>
                <div>CLS: {metrics.cls ? 
                  <span className={metrics.cls <= 0.1 ? 'text-green-600' : metrics.cls <= 0.25 ? 'text-yellow-600' : 'text-red-600'}>
                    {metrics.cls.toFixed(4)}
                  </span> : 'N/A'}</div>
                <div>INP: {formatScore(metrics.inp, [200, 500])}</div>
              </div>
            </div>
            
            <div className="border-b pb-2">
              <h4 className="font-semibold text-gray-700 mb-1">Navigation Timing</h4>
              <div className="grid grid-cols-2 gap-2">
                <div>TTFB: {formatMs(metrics.ttfb)}</div>
                <div>DOM Ready: {formatMs(metrics.domContentLoaded)}</div>
                <div>Load Complete: {formatMs(metrics.loadTime)}</div>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-700 mb-1">Resources</h4>
              <div className="grid grid-cols-2 gap-2">
                <div>Count: {metrics.resourceCount || 'N/A'}</div>
                <div>Size: {formatKB(metrics.transferSize)}</div>
              </div>
            </div>
            
            <div className="mt-3 pt-2 border-t text-xs text-gray-500">
              <div className="mb-1">Performance Thresholds:</div>
              <div>🟢 Good 🟡 Needs Improvement 🔴 Poor</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
