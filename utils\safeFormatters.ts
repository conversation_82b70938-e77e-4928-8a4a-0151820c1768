/**
 * Safe Formatters for Financial Data
 * 
 * These utilities prevent TypeError: growth.replace is not a function
 * by safely handling null/undefined values in financial metrics
 */

/**
 * Safely handle string operations on potentially null/undefined values
 */
export function safeString(value: any, defaultValue: string = 'N/A'): string {
  if (value === null || value === undefined) {
    return defaultValue;
  }
  
  // If it's already a string, return it
  if (typeof value === 'string') {
    return value;
  }
  
  // Convert to string
  return String(value);
}

/**
 * Format growth percentage with arrow indicators, safely handling null values
 * This is the main fix for: TypeError: growth.replace is not a function
 */
export function formatGrowth(growth: any): string {
  const safeGrowth = safeString(growth);
  
  // Don't format if it's N/A
  if (safeGrowth === 'N/A') {
    return safeGrowth;
  }
  
  // Apply formatting logic safely
  return safeGrowth
    .replace(/\+/g, '↑')
    .replace(/-/g, '↓')
    .replace(/(\d+\.?\d*)%/g, '$1%'); // Preserve percentage formatting
}

/**
 * Format financial amount, safely handling null values
 */
export function formatAmount(amount: any): string {
  const safeAmount = safeString(amount);
  
  // Don't format if it's N/A
  if (safeAmount === 'N/A') {
    return safeAmount;
  }
  
  // Apply any existing amount formatting logic here
  return safeAmount;
}

/**
 * Check if a value has meaningful data (not null/N/A/empty)
 */
export function hasValidData(value: any): boolean {
  if (value === null || value === undefined) {
    return false;
  }
  
  if (typeof value === 'string') {
    const normalized = value.trim().toLowerCase();
    return normalized !== '' && 
           normalized !== 'n/a' && 
           normalized !== 'na' &&
           normalized !== 'not applicable' &&
           normalized !== 'not available' &&
           normalized !== 'not provided' &&
           normalized !== 'not specified';
  }
  
  return true;
}

/**
 * Check if a financial metric object has any valid data
 */
export function hasValidFinancialMetric(metric: any): boolean {
  if (!metric || typeof metric !== 'object') {
    return false;
  }
  
  const { current, previous, growth } = metric;
  
  return hasValidData(current) || hasValidData(previous) || hasValidData(growth);
}

/**
 * Safe wrapper for any function that might throw due to null values
 */
export function safeExecute<T>(fn: () => T, fallback: T): T {
  try {
    return fn();
  } catch (error) {
    console.warn('Safe execution caught error:', error);
    return fallback;
  }
}

/**
 * Create a safe version of any formatter function
 */
export function makeSafeFormatter<T extends any[], R>(
  formatter: (...args: T) => R,
  fallback: R
) {
  return (...args: T): R => {
    return safeExecute(() => formatter(...args), fallback);
  };
}

// Export a safe version of formatGrowth that will never throw
export const safeFormatGrowth = makeSafeFormatter(formatGrowth, 'N/A');
export const safeFormatAmount = makeSafeFormatter(formatAmount, 'N/A');
