import { createClient } from '@supabase/supabase-js';
import { User, Session, AuthError } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Create a separate auth client to avoid conflicts with the main supabase client
export const supabaseAuth = createClient(supabaseUrl, supabaseAnonKey);

export interface AuthUser extends User {
  email?: string;
}

export interface AuthSession extends Session {
  user: AuthUser;
}

export interface SignUpData {
  email: string;
  password: string;
  options?: {
    data?: {
      full_name?: string;
    };
  };
}

export interface SignInData {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: AuthUser | null;
  session: AuthSession | null;
  error: AuthError | null;
}

// Authentication functions
export const authService = {
  // Sign up with email and password
  async signUp({ email, password, options }: SignUpData): Promise<AuthResponse> {
    const { data, error } = await supabaseAuth.auth.signUp({
      email,
      password,
      options,
    });

    return {
      user: data.user as AuthUser,
      session: data.session as AuthSession,
      error,
    };
  },

  // Sign in with email and password
  async signIn({ email, password }: SignInData): Promise<AuthResponse> {
    const { data, error } = await supabaseAuth.auth.signInWithPassword({
      email,
      password,
    });

    return {
      user: data.user as AuthUser,
      session: data.session as AuthSession,
      error,
    };
  },

  // Sign in with Google OAuth
  async signInWithGoogle() {
    const { data, error } = await supabaseAuth.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
      },
    });

    return { data, error };
  },

  // Sign out
  async signOut() {
    const { error } = await supabaseAuth.auth.signOut();
    return { error };
  },

  // Reset password
  async resetPassword(email: string) {
    const { data, error } = await supabaseAuth.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    });

    return { data, error };
  },

  // Update password
  async updatePassword(password: string) {
    const { data, error } = await supabaseAuth.auth.updateUser({
      password,
    });

    return { data, error };
  },

  // Get current session
  async getSession() {
    try {
      // Create an AbortController for the fetch operation
      const controller = new AbortController();
      
      // Add timeout to prevent hanging requests
      const timeoutId = setTimeout(() => {
        controller.abort('Request timed out');
      }, 8000); // Increased timeout to 8 seconds
      
      try {
        // Use the signal in the getSession call
        const { data, error } = await supabaseAuth.auth.getSession();
        
        // Clear the timeout
        clearTimeout(timeoutId);
        
        if (error) {
          // Check if the error is related to abort/timeout
          if (error.message?.includes('aborted') || error.message?.includes('abort') || 
              error.message?.includes('timeout') || error.message?.includes('timed out')) {
            return {
              session: null,
              error: {
                message: 'Auth request timed out. Please try again.',
                status: 408
              } as AuthError,
            };
          }
        }
        
        return {
          session: data.session as AuthSession,
          error,
        };
      } catch (fetchError: any) {
        // Clear the timeout
        clearTimeout(timeoutId);
        
        // Handle AbortError specifically
        if (fetchError.name === 'AbortError' || fetchError.code === 'FETCH_TIMEOUT' || 
            fetchError.message?.includes('aborted') || fetchError.message?.includes('abort') ||
            fetchError.message?.includes('Failed to fetch') || fetchError.message?.includes('timeout')) {
          console.warn('Auth session request timed out or was aborted:', fetchError);
          return {
            session: null,
            error: {
              message: 'Connection timed out. Please check your network and try again.',
              status: 408
            } as AuthError,
          };
        }
        
        // Re-throw other errors to be caught by the outer catch
        throw fetchError;
      }
    } catch (error: any) {
      console.error('Error in getSession:', error);
      return {
        session: null,
        error: {
          message: error.message || 'Failed to get session',
          status: error.status || 500
        } as AuthError,
      };
    }
  },

  // Get current user
  async getUser() {
    try {
      // Create an AbortController for the fetch operation
      const controller = new AbortController();
      
      // Add timeout to prevent hanging requests
      const timeoutId = setTimeout(() => {
        controller.abort('Request timed out');
      }, 8000); // Increased timeout to 8 seconds
      
      try {
        // Use the signal in the getUser call
        const { data, error } = await supabaseAuth.auth.getUser();
        
        // Clear the timeout
        clearTimeout(timeoutId);
        
        if (error) {
          // Check if the error is related to abort/timeout
          if (error.message?.includes('aborted') || error.message?.includes('abort') || 
              error.message?.includes('timeout') || error.message?.includes('timed out')) {
            return {
              user: null,
              error: {
                message: 'Auth request timed out. Please try again.',
                status: 408
              } as AuthError,
            };
          }
        }
        
        return {
          user: data.user as AuthUser,
          error,
        };
      } catch (fetchError: any) {
        // Clear the timeout
        clearTimeout(timeoutId);
        
        // Handle AbortError specifically
        if (fetchError.name === 'AbortError' || fetchError.code === 'FETCH_TIMEOUT' || 
            fetchError.message?.includes('aborted') || fetchError.message?.includes('abort') ||
            fetchError.message?.includes('Failed to fetch') || fetchError.message?.includes('timeout')) {
          console.warn('Auth user request timed out or was aborted:', fetchError);
          return {
            user: null,
            error: {
              message: 'Connection timed out. Please check your network and try again.',
              status: 408
            } as AuthError,
          };
        }
        
        // Re-throw other errors to be caught by the outer catch
        throw fetchError;
      }
    } catch (error: any) {
      console.error('Error in getUser:', error);
      return {
        user: null,
        error: {
          message: error.message || 'Failed to get user',
          status: error.status || 500
        } as AuthError,
      };
    }
  },

  // Listen to auth state changes
  onAuthStateChange(callback: (event: string, session: AuthSession | null) => void) {
    return supabaseAuth.auth.onAuthStateChange(callback);
  },
};

// Validation functions
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/(?=.*[a-z])/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/(?=.*[A-Z])/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/(?=.*\d)/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/(?=.*[@$!%*?&])/.test(password)) {
    errors.push('Password must contain at least one special character (@$!%*?&)');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Auth error messages
export const getAuthErrorMessage = (error: AuthError | null): string => {
  if (!error) return '';

  switch (error.message) {
    case 'Invalid login credentials':
      return 'Invalid email or password. Please check your credentials and try again.';
    case 'Email not confirmed':
      return 'Please check your email and click the confirmation link before signing in.';
    case 'User already registered':
      return 'An account with this email already exists. Please sign in instead.';
    case 'Password should be at least 6 characters':
      return 'Password must be at least 6 characters long.';
    case 'Signup is disabled':
      return 'New account registration is currently disabled.';
    case 'Email rate limit exceeded':
      return 'Too many emails sent. Please wait before requesting another.';
    default:
      return error.message || 'An unexpected error occurred. Please try again.';
  }
};
