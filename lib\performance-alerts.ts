// Performance monitoring and alerting system

interface PerformanceThresholds {
  fcp: number;      // First Contentful Paint
  lcp: number;      // Largest Contentful Paint
  inp: number;      // Interaction to Next Paint
  cls: number;      // Cumulative Layout Shift
  ttfb: number;     // Time to First Byte
  queryTime: number; // Database query time
  bundleSize: number; // Bundle size in KB
}

// Performance thresholds for alerts
const PERFORMANCE_THRESHOLDS: PerformanceThresholds = {
  fcp: 1800,    // 1.8 seconds
  lcp: 2500,    // 2.5 seconds
  inp: 200,     // 200ms
  cls: 0.1,     // 0.1 layout shift
  ttfb: 800,    // 800ms
  queryTime: 200, // 200ms for database queries
  bundleSize: 200, // 200KB bundle size
};

interface PerformanceAlert {
  metric: string;
  value: number;
  threshold: number;
  severity: 'warning' | 'critical';
  timestamp: Date;
  page?: string;
}

class PerformanceMonitor {
  private alerts: PerformanceAlert[] = [];
  private isEnabled: boolean = true;

  constructor() {
    // Only enable in production or when explicitly enabled
    this.isEnabled = process.env.NODE_ENV === 'production' || 
                     process.env.ENABLE_PERFORMANCE_MONITORING === 'true';
  }

  // Check if a metric exceeds threshold
  private checkThreshold(metric: string, value: number, page?: string): void {
    if (!this.isEnabled) return;

    const threshold = PERFORMANCE_THRESHOLDS[metric as keyof PerformanceThresholds];
    if (!threshold) return;

    let severity: 'warning' | 'critical' = 'warning';
    let shouldAlert = false;

    // Determine severity and if we should alert
    switch (metric) {
      case 'fcp':
      case 'lcp':
      case 'ttfb':
      case 'queryTime':
        shouldAlert = value > threshold;
        severity = value > threshold * 1.5 ? 'critical' : 'warning';
        break;
      case 'inp':
        shouldAlert = value > threshold;
        severity = value > 500 ? 'critical' : 'warning';
        break;
      case 'cls':
        shouldAlert = value > threshold;
        severity = value > 0.25 ? 'critical' : 'warning';
        break;
      case 'bundleSize':
        shouldAlert = value > threshold;
        severity = value > threshold * 1.2 ? 'critical' : 'warning';
        break;
    }

    if (shouldAlert) {
      this.createAlert(metric, value, threshold, severity, page);
    }
  }

  // Create and store alert
  private createAlert(
    metric: string, 
    value: number, 
    threshold: number, 
    severity: 'warning' | 'critical',
    page?: string
  ): void {
    const alert: PerformanceAlert = {
      metric,
      value,
      threshold,
      severity,
      timestamp: new Date(),
      page,
    };

    this.alerts.push(alert);
    
    // Log alert
    const emoji = severity === 'critical' ? '🚨' : '⚠️';
    const pageInfo = page ? ` on ${page}` : '';
    
    console.warn(
      `${emoji} Performance Alert: ${metric.toUpperCase()} = ${value}${metric === 'cls' ? '' : 'ms'} ` +
      `(threshold: ${threshold}${metric === 'cls' ? '' : 'ms'})${pageInfo}`
    );

    // In production, you would send this to your monitoring service
    this.sendAlert(alert);
  }

  // Send alert to monitoring service (implement based on your service)
  private sendAlert(alert: PerformanceAlert): void {
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to monitoring service
      // fetch('/api/alerts', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(alert)
      // });
    }
  }

  // Monitor Web Vitals
  public monitorWebVital(metric: string, value: number, page?: string): void {
    this.checkThreshold(metric.toLowerCase(), value, page);
  }

  // Monitor database query performance
  public monitorQuery(queryName: string, duration: number): void {
    this.checkThreshold('queryTime', duration);
    
    // Log query performance in development
    if (process.env.NODE_ENV === 'development') {
      const status = duration > PERFORMANCE_THRESHOLDS.queryTime ? '🐌 SLOW' : '⚡ FAST';
      console.log(`[Query] ${queryName}: ${duration.toFixed(2)}ms ${status}`);
    }
  }

  // Monitor bundle size
  public monitorBundleSize(size: number, page: string): void {
    this.checkThreshold('bundleSize', size / 1024, page); // Convert to KB
  }

  // Get recent alerts
  public getRecentAlerts(hours: number = 24): PerformanceAlert[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.alerts.filter(alert => alert.timestamp > cutoff);
  }

  // Get performance summary
  public getPerformanceSummary(): {
    totalAlerts: number;
    criticalAlerts: number;
    warningAlerts: number;
    recentAlerts: PerformanceAlert[];
  } {
    const recentAlerts = this.getRecentAlerts();
    
    return {
      totalAlerts: this.alerts.length,
      criticalAlerts: this.alerts.filter(a => a.severity === 'critical').length,
      warningAlerts: this.alerts.filter(a => a.severity === 'warning').length,
      recentAlerts,
    };
  }

  // Clear old alerts (call periodically)
  public clearOldAlerts(days: number = 7): void {
    const cutoff = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    this.alerts = this.alerts.filter(alert => alert.timestamp > cutoff);
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// Enhanced analytics function that includes alerting
export function trackPerformanceWithAlerts(metric: string, value: number, page?: string): void {
  // Track the metric
  performanceMonitor.monitorWebVital(metric, value, page);
  
  // Also log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Performance] ${metric}: ${value}ms${page ? ` on ${page}` : ''}`);
  }
}

// Enhanced query monitoring with alerts
export function monitorQueryWithAlerts<T>(
  queryName: string,
  queryFn: () => Promise<T>
): Promise<T> {
  const startTime = performance.now();
  
  return queryFn()
    .then((result) => {
      const duration = performance.now() - startTime;
      performanceMonitor.monitorQuery(queryName, duration);
      return result;
    })
    .catch((error) => {
      const duration = performance.now() - startTime;
      performanceMonitor.monitorQuery(`${queryName} (FAILED)`, duration);
      throw error;
    });
}

export default performanceMonitor;
