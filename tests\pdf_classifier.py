#!/usr/bin/env python3
"""
Simple PDF Classifier

Takes a PDF URL, reads first 25 pages, and classifies the document.
"""

import os
import sys
import requests
import io
from openai import OpenAI
import pdfplumber

# Document types we can classify
DOCUMENT_TYPES = [
    "earnings_call",
    "contract_award",
    "results_announcement",
    "insider_trading",
    "merger_acquisition",
    "agm_egm",
    "board_meeting",
    "regulatory_disclosure",
    "others"
]

def download_pdf(url):
    """Download PDF from URL"""
    print(f"Downloading PDF from: {url}")

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }

    try:
        response = requests.get(url, headers=headers, timeout=60)
        response.raise_for_status()
        print(f"Downloaded {len(response.content)} bytes")
        return response.content
    except Exception as e:
        print(f"Error downloading PDF: {e}")
        return None


def extract_text_from_pdf(pdf_content):
    """Extract text from first 25 pages of PDF"""
    text_content = ""

    try:
        with pdfplumber.open(io.BytesIO(pdf_content)) as pdf:
            # Process only first 25 pages
            pages_to_read = min(len(pdf.pages), 25)
            print(f"Reading first {pages_to_read} pages...")

            for page_num in range(pages_to_read):
                page = pdf.pages[page_num]
                page_text = page.extract_text()
                if page_text:
                    text_content += page_text + "\n"

        print(f"Extracted {len(text_content)} characters")
        return text_content

    except Exception as e:
        print(f"Error extracting text: {e}")
        return ""


def extract_financial_metrics(response_text):
    """Extract financial metrics from AI response"""
    def safe_convert(value_str):
        """Safely convert string to number or return null"""
        if not value_str or value_str.lower() in ['null', 'n/a', 'na', 'not available', '-']:
            return None
        try:
            # Remove common currency symbols and text
            cleaned = value_str.replace('Rs.', '').replace('cr', '').replace('₹', '').replace(',', '').strip()
            return float(cleaned) if cleaned else None
        except:
            return value_str.strip() if value_str.strip() else None

    # Initialize financial metrics structure
    financial_metrics = {
        "revenue": {"current": None, "previous": None, "growth": None},
        "ebitda": {"current": None, "previous": None, "growth": None},
        "pat": {"current": None, "previous": None, "growth": None},
        "other_metrics": {
            "earnings_per_share": None,
            "turnover": {"current": None, "previous": None, "growth": None},
            "net_worth": {"current": None, "previous": None, "growth": None},
            "net_profits": {"current": None, "previous": None, "growth": None}
        }
    }

    lines = response_text.split('\n')

    for line in lines:
        line = line.strip()
        if ':' in line:
            key, value = line.split(':', 1)
            key = key.strip().lower()
            value = value.strip()

            # Revenue metrics
            if 'revenue current' in key:
                financial_metrics["revenue"]["current"] = safe_convert(value)
            elif 'revenue previous' in key:
                financial_metrics["revenue"]["previous"] = safe_convert(value)
            elif 'revenue growth' in key:
                financial_metrics["revenue"]["growth"] = safe_convert(value)

            # EBITDA metrics
            elif 'ebitda current' in key:
                financial_metrics["ebitda"]["current"] = safe_convert(value)
            elif 'ebitda previous' in key:
                financial_metrics["ebitda"]["previous"] = safe_convert(value)
            elif 'ebitda growth' in key:
                financial_metrics["ebitda"]["growth"] = safe_convert(value)

            # PAT metrics
            elif 'pat current' in key:
                financial_metrics["pat"]["current"] = safe_convert(value)
            elif 'pat previous' in key:
                financial_metrics["pat"]["previous"] = safe_convert(value)
            elif 'pat growth' in key:
                financial_metrics["pat"]["growth"] = safe_convert(value)

            # Other metrics
            elif key == 'eps':
                financial_metrics["other_metrics"]["earnings_per_share"] = safe_convert(value)
            elif 'turnover current' in key:
                financial_metrics["other_metrics"]["turnover"]["current"] = safe_convert(value)
            elif 'turnover previous' in key:
                financial_metrics["other_metrics"]["turnover"]["previous"] = safe_convert(value)
            elif 'turnover growth' in key:
                financial_metrics["other_metrics"]["turnover"]["growth"] = safe_convert(value)
            elif 'net worth current' in key:
                financial_metrics["other_metrics"]["net_worth"]["current"] = safe_convert(value)
            elif 'net worth previous' in key:
                financial_metrics["other_metrics"]["net_worth"]["previous"] = safe_convert(value)
            elif 'net worth growth' in key:
                financial_metrics["other_metrics"]["net_worth"]["growth"] = safe_convert(value)
            elif 'net profits current' in key:
                financial_metrics["other_metrics"]["net_profits"]["current"] = safe_convert(value)
            elif 'net profits previous' in key:
                financial_metrics["other_metrics"]["net_profits"]["previous"] = safe_convert(value)
            elif 'net profits growth' in key:
                financial_metrics["other_metrics"]["net_profits"]["growth"] = safe_convert(value)

    return financial_metrics


def has_valid_financial_data(financial_metrics):
    """Check if financial metrics contain any valid data"""
    if not financial_metrics:
        return False

    # Check main metrics
    for metric in ['revenue', 'ebitda', 'pat']:
        if metric in financial_metrics:
            metric_data = financial_metrics[metric]
            if any(metric_data.get(key) is not None for key in ['current', 'previous', 'growth']):
                return True

    # Check other metrics
    other_metrics = financial_metrics.get('other_metrics', {})
    if other_metrics.get('earnings_per_share') is not None:
        return True

    for metric in ['turnover', 'net_worth', 'net_profits']:
        if metric in other_metrics:
            metric_data = other_metrics[metric]
            if any(metric_data.get(key) is not None for key in ['current', 'previous', 'growth']):
                return True

    return False


def classify_document(text_content):
    """Classify document using OpenAI"""

    # Get OpenAI API key
    api_key = "********************************************************************************************************************************************************************"
    if not api_key:
        print("Error: Please set OPENAI_API_KEY environment variable")
        return {
            "classification": "others",
            "summary": "No OpenAI API key found",
            "rationale": "Please set OPENAI_API_KEY environment variable"
        }

    print(f"Using OpenAI API (key starts with: {api_key[:10]}...)")

    client = OpenAI(api_key=api_key)

    # Create simple prompt
    document_types_text = ", ".join(DOCUMENT_TYPES)

    prompt = f"""
You're a world class financial analyst and your job is to read through all Financial Announcements and Disclosures made by publicly listed companies in India via the exchange. You will then classify these into:
1. AGM Announcement: if the document is announcing the Annual General Meeting for the company
2. Quarterly Results: If the announcement is financial report of the quarterly performance of the company.
3. Earnings Call Intimation: If the announcement is intimating the public about the date and time of the earnings call to discuss the quarterly results.
4. Contract Award: In case the announcement tells the public about the contract / tender the company has won.
5. Insider Trading: In case the the company has disclosed any insider trades taking place.
6. M&A: In case the company is disclosing any merger and aquisition activity.
7. Boarding meeting: If the company is informing about the date, time and agenda of any scheduled board meeting.
8. others for everything else.

Document text (first 8000 characters):
{text_content[:8000]}

For financial documents (Quarterly Results, Earnings Call, AGM with financial data), also extract key financial metrics if available:
- Revenue (current, previous, growth %)
- EBITDA (current, previous, growth %)
- PAT/Net Profit (current, previous, growth %)
- Other metrics like EPS, Turnover, Net Worth if mentioned

Respond in this format:
Category: [category name]
Summary: [brief summary]
Rationale: [reasoning for classification]

Financial Metrics (only if this is a financial document with numerical data):
Revenue Current: [amount in Rs. cr or null]
Revenue Previous: [amount in Rs. cr or null]
Revenue Growth: [percentage or null]
EBITDA Current: [amount in Rs. cr or null]
EBITDA Previous: [amount in Rs. cr or null]
EBITDA Growth: [percentage or null]
PAT Current: [amount in Rs. cr or null]
PAT Previous: [amount in Rs. cr or null]
PAT Growth: [percentage or null]
EPS: [earnings per share or null]
Turnover Current: [amount in Rs. cr or null]
Turnover Previous: [amount in Rs. cr or null]
Turnover Growth: [percentage or null]
Net Worth Current: [amount in Rs. cr or null]
Net Worth Previous: [amount in Rs. cr or null]
Net Worth Growth: [percentage or null]
Net Profits Current: [amount in Rs. cr or null]
Net Profits Previous: [amount in Rs. cr or null]
Net Profits Growth: [percentage or null]
"""

    try:
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You're a world class Financial Analyst employed a top hedgefund that buys and holds long positions in India in specific. Your job is to help sort information for the partners in your firm."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=300
        )

        result_text = response.choices[0].message.content.strip()

        # Parse the structured response
        classification = "others"
        summary = "Document analysis completed"
        rationale = "Default classification"
        financial_metrics = None

        lines = result_text.split('\n')

        # Extract structured fields
        for line in lines:
            line = line.strip()
            if line.startswith('Category:'):
                category_text = line.replace('Category:', '').strip().lower()
                # Map category text to our classification types
                if "agm announcement" in category_text:
                    classification = "agm_egm"
                elif "quarterly results" in category_text:
                    classification = "results_announcement"
                elif "earnings call" in category_text:
                    classification = "earnings_call"
                elif "contract award" in category_text:
                    classification = "contract_award"
                elif "insider trading" in category_text:
                    classification = "insider_trading"
                elif "m&a" in category_text:
                    classification = "merger_acquisition"
                elif "boarding meeting" in category_text or "board meeting" in category_text:
                    classification = "board_meeting"
                else:
                    classification = "others"
            elif line.startswith('Summary:'):
                summary = line.replace('Summary:', '').strip()
            elif line.startswith('Rationale:'):
                rationale = line.replace('Rationale:', '').strip()

        # Extract financial metrics if present
        financial_metrics = extract_financial_metrics(result_text)

        result = {
            "classification": classification,
            "summary": summary,
            "rationale": rationale
        }

        # Add financial metrics only if they contain valid data
        if financial_metrics and has_valid_financial_data(financial_metrics):
            result["financial_metrics"] = financial_metrics

        return result

    except Exception as e:
        print(f"Error with OpenAI: {e}")
        return {
            "classification": "others",
            "summary": f"OpenAI API error: {str(e)}",
            "rationale": f"Classification failed due to API error: {str(e)}"
        }


def analyze_pdf(pdf_url):
    """Main function to analyze PDF"""
    print(f"Analyzing PDF: {pdf_url}")

    # Step 1: Download PDF
    pdf_content = download_pdf(pdf_url)
    if not pdf_content:
        return {"error": "Could not download PDF"}

    # Step 2: Extract text
    text_content = extract_text_from_pdf(pdf_content)
    if not text_content:
        return {"error": "Could not extract text from PDF"}

    # Step 3: Classify
    classification_result = classify_document(text_content)

    # Handle both old string format and new dict format
    if isinstance(classification_result, dict):
        result = {
            "url": pdf_url,
            "classification": classification_result["classification"],
            "summary": classification_result["summary"],
            "rationale": classification_result["rationale"],
            "text_length": len(text_content)
        }

        # Add financial metrics if present
        if "financial_metrics" in classification_result:
            result["financial_metrics"] = classification_result["financial_metrics"]

        return result
    else:
        # Backward compatibility for string return
        return {
            "url": pdf_url,
            "classification": classification_result,
            "text_length": len(text_content)
        }


def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python pdf_classifier.py <PDF_URL>")
        print("Example: python pdf_classifier.py https://example.com/document.pdf")
        return

    pdf_url = sys.argv[1]

    # Analyze the PDF
    result = analyze_pdf(pdf_url)

    # Print results
    print("\n" + "="*60)
    print("PDF CLASSIFICATION RESULT")
    print("="*60)

    if "error" in result:
        print(f"❌ Error: {result['error']}")
    else:
        print(f"✅ SUCCESS")
        print(f"URL: {result['url']}")
        print(f"Classification: {result['classification']}")
        print(f"Text Length: {result['text_length']} characters")

        # Show summary and rationale if available
        if "summary" in result and result['summary']:
            print(f"\nSummary:")
            print(f"{result['summary']}")

        if "rationale" in result and result['rationale']:
            print(f"\nFull Response:")
            print(f"{result['rationale']}")

        # Display financial metrics if present
        if "financial_metrics" in result:
            print(f"\n💰 Financial Metrics:")
            fm = result['financial_metrics']

            # Revenue
            if fm['revenue']['current'] is not None:
                print(f"  Revenue: {fm['revenue']['current']} Rs. cr (Current)")
                if fm['revenue']['previous'] is not None:
                    print(f"           {fm['revenue']['previous']} Rs. cr (Previous)")
                if fm['revenue']['growth'] is not None:
                    print(f"           {fm['revenue']['growth']}% Growth")

            # EBITDA
            if fm['ebitda']['current'] is not None:
                print(f"  EBITDA:  {fm['ebitda']['current']} Rs. cr (Current)")
                if fm['ebitda']['previous'] is not None:
                    print(f"           {fm['ebitda']['previous']} Rs. cr (Previous)")
                if fm['ebitda']['growth'] is not None:
                    print(f"           {fm['ebitda']['growth']}% Growth")

            # PAT
            if fm['pat']['current'] is not None:
                print(f"  PAT:     {fm['pat']['current']} Rs. cr (Current)")
                if fm['pat']['previous'] is not None:
                    print(f"           {fm['pat']['previous']} Rs. cr (Previous)")
                if fm['pat']['growth'] is not None:
                    print(f"           {fm['pat']['growth']}% Growth")

            # Other metrics
            other = fm['other_metrics']
            if other['earnings_per_share'] is not None:
                print(f"  EPS:     {other['earnings_per_share']} Rs.")

            if other['turnover']['current'] is not None:
                print(f"  Turnover: {other['turnover']['current']} Rs. cr (Current)")
                if other['turnover']['growth'] is not None:
                    print(f"            {other['turnover']['growth']}% Growth")

    print("="*60)


if __name__ == "__main__":
    main()
