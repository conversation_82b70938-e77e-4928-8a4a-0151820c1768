"use client";

import { useEffect, useMemo, useRef, useState, useCallback } from "react";
import { createPortal } from "react-dom";
import { Search, Check, Loader2, Building2 } from "@/lib/icons";
import { AvailableCompany, Watchlist } from "@/lib/watchlist/types";
import { fetchMasterCompanies } from "@/lib/supabase";
import { debounce } from "@/lib/watchlist/utils";

interface AddCompanyDropdownProps {
  buttonId?: string; // for aria-controls linkage
  watchlists: Watchlist[];
  defaultWatchlistId?: string;
  existingKeys?: string[]; // lowercased keys: company_name, nse_symbol, bse_code to exclude
  onAddCompany: (
    companies: AvailableCompany[] | AvailableCompany,
    watchlistId: string
  ) => Promise<{ success: boolean; error?: string }>;
}

export default function AddCompanyDropdown({
  buttonId,
  watchlists,
  defaultWatchlistId,
  existingKeys = [],
  onAddCompany,
}: AddCompanyDropdownProps) {
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const [suggestions, setSuggestions] = useState<AvailableCompany[]>([]);
  const [highlightIndex, setHighlightIndex] = useState<number>(-1);
  const [watchlistId, setWatchlistId] = useState<string>(
    defaultWatchlistId || watchlists[0]?.id || ""
  );
  const [selected, setSelected] = useState<AvailableCompany[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const controlId = buttonId || "add-company-dropdown";

  const inputRef = useRef<HTMLInputElement>(null);
  const listboxRef = useRef<HTMLUListElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const portalRef = useRef<HTMLDivElement | null>(null);

  const navByKeyboardRef = useRef<boolean>(false);
  // Tracks existing companies for the currently selected watchlist
  const existingKeySetRef = useRef<Set<string>>(new Set());


  // Keep selected watchlist in sync with props
  useEffect(() => {
    if (!watchlistId) {
      const nextId = defaultWatchlistId || watchlists[0]?.id || "";
      if (nextId) setWatchlistId(nextId);
    }
  }, [defaultWatchlistId, watchlists, watchlistId]);

  // Build a set of existing keys for currently selected watchlist
  useEffect(() => {
    // existingKeys prop comes from the current page’s selected watchlist; if the target watchlist
    // changes inside this dropdown, we need to fetch that watchlist’s items to compute keys.
    async function buildExistingSet() {
      const norm = (v?: string) => String(v || '').toLowerCase().trim();

      try {
        // If watchlistId matches the default/current list from parent, we can rely on provided existingKeys
        const propSet = new Set((existingKeys || []).map(k => String(k).toLowerCase().trim()));
        // If user changed the target watchlist, try to fetch items for that watchlist
        if (watchlistId && defaultWatchlistId && watchlistId !== defaultWatchlistId) {
          try {
            const res = await fetch(`/api/watchlist/items?watchlist_id=${encodeURIComponent(watchlistId)}`);
            if (res.ok) {
              const json = await res.json();
              const items = Array.isArray(json.items) ? json.items : [];
              const keys = items.flatMap((it: any) => [it.company_name, it.nse_symbol, it.bse_code])
                                 .filter(Boolean)
                                 .map(norm);
              existingKeySetRef.current = new Set(keys);
            } else {
              // If we can't fetch target watchlist items, don't exclude anything
              existingKeySetRef.current = new Set();
            }
          } catch {
            // If we can't fetch target watchlist items, don't exclude anything
            existingKeySetRef.current = new Set();
          }
        } else {
          // Same watchlist as parent selection; use provided keys
          existingKeySetRef.current = propSet;
        }
      } finally {
        // Loading state removed
      }
    }
    buildExistingSet();
  }, [watchlistId, defaultWatchlistId, existingKeys]);

  // Debounced search
  const doSearch = useMemo(
    () =>
      debounce(async (term: string) => {
        setLoading(true);
        try {
          const results = await fetchMasterCompanies(term, 50);
          // Filter out existing companies from the currently selected watchlist
          const norm = (v?: string) => String(v || '').toLowerCase().trim();
          const existingSet = existingKeySetRef.current;
          const filtered = results.filter(r => {
            const keys = [r.company_name, r.nse_symbol, r.bse_code].filter(Boolean).map(norm);
            return !keys.some(k => existingSet.has(k));
          });
          setSuggestions(filtered);
          setHighlightIndex(filtered.length ? 0 : -1);
        } finally {
          setLoading(false);
        }
      }, 200),
    []
  );

  useEffect(() => {
    if (!open) return;
    doSearch(query);
  }, [open, query, doSearch]);

  // Close when clicking outside, restore focus to button
  useEffect(() => {
    function onDocClick(e: MouseEvent) {
      if (!open) return;
      const target = e.target as Node | null;
      const path = (e as any).composedPath ? (e as any).composedPath() as Node[] : [];
      const withinTrigger = containerRef.current ? (path.length ? path.includes(containerRef.current) : containerRef.current.contains(target as Node)) : false;
      const withinPortal = portalRef.current ? (path.length ? path.includes(portalRef.current) : portalRef.current.contains(target as Node)) : false;
      if (withinTrigger || withinPortal) return; // clicks inside keep it open
      setOpen(false);
      setHighlightIndex(-1);
      requestAnimationFrame(() => buttonRef.current?.focus());
    }
    document.addEventListener('mousedown', onDocClick, true);
    return () => document.removeEventListener('mousedown', onDocClick, true);
  }, [open]);
  useEffect(() => {
    if (!open) return;
    // Create a portal container for the dropdown to avoid clipping/collision
    const div = document.createElement('div');
    div.style.position = 'absolute';
    div.style.top = '0';
    div.style.left = '0';
    div.style.zIndex = '1000';
    document.body.appendChild(div);
    portalRef.current = div;

    return () => {
      if (portalRef.current) {
        document.body.removeChild(portalRef.current);
        portalRef.current = null;
      }
    };
  }, [open]);

  // Position portal near the button on open/resize/scroll
  useEffect(() => {
    function positionDropdown() {
      if (!open || !portalRef.current || !containerRef.current) return;
      const btn = containerRef.current.getBoundingClientRect();
      const vw = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);
      const margin = 8;
      const width = Math.min(480, Math.floor(vw * 0.9));

      // Default: align right edge of panel with right edge of button group
      let left = btn.right - width;
      // If that would push panel off the left edge, try flipping to align left edges
      if (left < margin) {
        left = btn.left;
      }
      // Clamp within viewport
      left = Math.max(margin, Math.min(left, vw - width - margin));

      const top = btn.bottom + 8; // small gap below button

      const el = portalRef.current;
      el.style.width = width + 'px';
      el.style.maxWidth = '95vw';
      el.style.left = left + 'px';
      el.style.top = top + 'px';
    }
    positionDropdown();
    window.addEventListener('resize', positionDropdown);
    window.addEventListener('scroll', positionDropdown, true);
    return () => {
      window.removeEventListener('resize', positionDropdown);
      window.removeEventListener('scroll', positionDropdown, true);
    };
  }, [open]);

  // Keep dropdown open on internal clicks (especially via portal)
  useEffect(() => {
    function onMouseDown(e: MouseEvent) {
      if (!open) return;
      const target = e.target as Node | null;
      const path = (e as any).composedPath ? (e as any).composedPath() as Node[] : [];
      const withinTrigger = containerRef.current ? (path.length ? path.includes(containerRef.current) : containerRef.current.contains(target as Node)) : false;
      const withinPortal = portalRef.current ? (path.length ? path.includes(portalRef.current) : portalRef.current.contains(target as Node)) : false;
      if (withinTrigger || withinPortal) {
        // Prevent outside-click handler or parent handlers from closing it
        e.stopPropagation();
      }
    }
    document.addEventListener('mousedown', onMouseDown, true);
    return () => document.removeEventListener('mousedown', onMouseDown, true);
  }, [open]);

  // Keyboard navigation works even if focus is on chips/list, not only the input
  useEffect(() => {
    function onKey(e: KeyboardEvent) {
      if (!open) return;
      const target = e.target as Node | null;
      const path = (e as any).composedPath ? (e as any).composedPath() as Node[] : [];
      const withinTrigger = containerRef.current ? (path.length ? path.includes(containerRef.current) : containerRef.current.contains(target as Node)) : false;
      const withinPortal = portalRef.current ? (path.length ? path.includes(portalRef.current) : portalRef.current.contains(target as Node)) : false;
      if (!withinTrigger && !withinPortal) return;

      const el = e.target as HTMLElement | null;
      const tag = (el?.tagName || '').toLowerCase();
      const isTextInput = tag === 'input' || tag === 'textarea' || tag === 'select' || (el?.isContentEditable ?? false);
      // When focus is in the text input, let the input's own handler manage keys to avoid double handling
      if (isTextInput) return;

      if (e.key === 'ArrowDown') {
        e.preventDefault();
        navByKeyboardRef.current = true;
        setHighlightIndex((i) => (suggestions.length ? (i + 1) % suggestions.length : -1));
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        navByKeyboardRef.current = true;
        setHighlightIndex((i) => (suggestions.length ? (i - 1 + suggestions.length) % suggestions.length : -1));
      } else if (e.key === 'Enter') {
        e.preventDefault();
        navByKeyboardRef.current = true;
        const idx = highlightIndex >= 0 ? highlightIndex : (suggestions.length > 0 ? 0 : -1);
        const item = idx >= 0 ? suggestions[idx] : undefined;
        if (item) {
          toggleSelect(item);
        }
      } else if (e.key === 'Escape') {
        e.preventDefault();
        setOpen(false);
        requestAnimationFrame(() => buttonRef.current?.focus());
      }
    }
    document.addEventListener('keydown', onKey, true);
    return () => document.removeEventListener('keydown', onKey, true);
  }, [open, suggestions, highlightIndex]);

  const onKeyDown = useCallback(
    async (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (!open && (e.key === "ArrowDown" || e.key === "ArrowUp")) {
        setOpen(true);
        return;
      }
      if (e.key === "ArrowDown") {
        e.preventDefault();
        navByKeyboardRef.current = true;
        setHighlightIndex((i) => suggestions.length ? (i + 1) % suggestions.length : -1);
      } else if (e.key === "ArrowUp") {
        e.preventDefault();
        navByKeyboardRef.current = true;
        setHighlightIndex((i) => suggestions.length ? (i - 1 + suggestions.length) % suggestions.length : -1);
      } else if (e.key === "Enter") {
        e.preventDefault();
        navByKeyboardRef.current = true;
        const idx = highlightIndex >= 0 ? highlightIndex : (suggestions.length > 0 ? 0 : -1);
        const item = idx >= 0 ? suggestions[idx] : undefined;
        if (item) {
          toggleSelect(item);
          requestAnimationFrame(() => inputRef.current?.focus());
        }
      } else if (e.key === "Escape") {
        e.preventDefault();
        setOpen(false);
        requestAnimationFrame(() => buttonRef.current?.focus());
      }
    },
    [open, suggestions, highlightIndex]
  );

  const toggleSelect = (company: AvailableCompany) => {
    const norm = (v?: string) => (v || '').toLowerCase().trim();
    const matches = (a: AvailableCompany, b: AvailableCompany) =>
      (a.nse_symbol && b.nse_symbol && norm(a.nse_symbol) === norm(b.nse_symbol)) ||
      (a.bse_code && b.bse_code && norm(a.bse_code) === norm(b.bse_code)) ||
      norm(a.company_name) === norm(b.company_name);
    setSelected(prev => {
      const exists = prev.some(c => matches(c, company));
      return exists ? prev.filter(c => !matches(c, company)) : [...prev, company];
    });
  };



  return (
    <div ref={containerRef} className="relative inline-block text-left">
      <button
        ref={buttonRef}
        id={controlId}
        type="button"
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 inline-flex items-center gap-2"
        aria-haspopup="listbox"
        aria-expanded={open}
        aria-controls={open ? `${controlId}-listbox` : undefined}
        onClick={() => {
          setOpen((o) => !o);
          // Focus after opening
          requestAnimationFrame(() => inputRef.current?.focus());
          // Ensure dropdown stays within viewport (always clamp inside container)
          requestAnimationFrame(() => {
            const dropdown = containerRef.current?.querySelector<HTMLDivElement>('[role="dialog"]');
            if (!dropdown) return;
            const vw = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);
            const desired = Math.min(512, Math.floor(vw * 0.9));
            dropdown.style.width = desired + 'px';
            dropdown.style.maxWidth = '95vw';
            // Position primarily to the right-edge of the button container
            dropdown.style.right = '0px';
            dropdown.style.left = 'auto';
            dropdown.style.transformOrigin = 'top right';
          });
        }}
      >
        Add Company
      </button>

      {open && portalRef.current && createPortal(
        <div
          className="z-[1000] w-[90vw] sm:w-[34rem] origin-top-right rounded-xl border border-slate-200 bg-white shadow-[0_10px_30px_rgba(2,6,23,0.15)] overflow-x-hidden"
          role="dialog"
          aria-label="Add company to watchlist"
          style={{ transformOrigin: 'top right' }}
        >
          {/* Search input + watchlist selector */}
          <div className="p-3 border-b border-slate-200 bg-slate-50/60 rounded-t-xl">
            <div className="flex gap-2">
              <div className="relative flex-1">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2">
                  <Search className="h-4 w-4 text-slate-400" />
                </div>
                <input
                  ref={inputRef}
                  type="text"
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  onKeyDown={onKeyDown}
                  placeholder="Search companies..."
                  className="w-full pl-8 pr-3 py-1.5 text-sm border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--strikedeck-blue)] focus:border-[var(--strikedeck-blue)] bg-white"
                  aria-autocomplete="list"
                  aria-controls={`${controlId}-listbox`}
                  aria-activedescendant={
                    highlightIndex >= 0 ? `${controlId}-option-${highlightIndex}` : undefined
                  }
                />
              </div>
              <div className="w-44">
                <label className="sr-only" htmlFor={`${controlId}-watchlist`}>
                  Watchlist
                </label>
                <select
                  id={`${controlId}-watchlist`}
                  value={watchlistId}
                  onChange={(e) => setWatchlistId(e.target.value)}
                  className="w-full px-2 py-2 rounded-md border border-slate-300 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
                  aria-label="Select watchlist"
                >
                  {watchlists.map((w) => (
                    <option key={w.id} value={w.id}>
                      {w.name} {w.is_default ? "(Default)" : ""}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Suggestions + selected chips + actions */}
          <div className="px-3 pt-2">
            {selected.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-2">
                {selected.map((s, i) => (
                  <span key={i} className="inline-flex items-center gap-1 bg-blue-50 text-blue-800 px-2 py-1 rounded text-xs border border-blue-200">
                    {s.company_name}
                    <button className="hover:bg-blue-100 rounded" onClick={() => toggleSelect(s)} aria-label={`Remove ${s.company_name}`}>
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>

          <ul
            id={`${controlId}-listbox`}
            ref={listboxRef}
            role="listbox"
            aria-label="Company suggestions"
            className="max-h-[22rem] min-h-[16rem] overflow-y-auto p-2 bg-white search-dropdown-scroll"
            style={{ scrollBehavior: 'auto' }}
          >
            {loading && (
              <li className="px-3 py-4 text-sm text-slate-600 flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" /> Searching companies...
              </li>
            )}

            {!loading && suggestions.length === 0 && (
              <li className="px-3 py-6 text-sm text-slate-500">Start typing to search companies</li>
            )}

            {!loading && suggestions.length > 0 && (
              <li className="px-3 pb-1 text-xs text-slate-500">Use Up/Down to navigate, Enter to select. Press Add to confirm.</li>
            )}

            {suggestions.map((c, idx) => {
              const highlighted = idx === highlightIndex;
              const selectedItem = selected.some(s => (s.nse_symbol && s.nse_symbol === c.nse_symbol) || s.company_name === c.company_name);
              return (
                <li
                  id={`${controlId}-option-${idx}`}
                  key={`${c.company_name}-${c.nse_symbol || c.bse_code || idx}`}
                  role="option"
                  aria-selected={highlighted}
                  tabIndex={-1}
                ref={idx === highlightIndex ? (el) => {
                  // Only auto-scroll when navigation explicitly happens by keyboard.
                  if (!navByKeyboardRef.current) return;
                  const li = el as HTMLLIElement | null;
                  const list = listboxRef.current;
                  if (!li || !list) return;
                  const elTop = li.offsetTop;
                  const elBottom = elTop + li.offsetHeight;
                  const viewTop = list.scrollTop;
                  const viewBottom = viewTop + list.clientHeight;
                  if (elTop < viewTop) list.scrollTop = elTop;
                  else if (elBottom > viewBottom) list.scrollTop = elBottom - list.clientHeight;
                  // reset flag so passive mouse moves won't auto-scroll
                  navByKeyboardRef.current = false;
                } : undefined}

                  onMouseEnter={() => setHighlightIndex(idx)}
                  onMouseDown={(e) => { e.preventDefault(); }}
                  onClick={(e) => { e.preventDefault(); e.stopPropagation(); toggleSelect(c); }}
                  className={`flex items-center justify-between px-3 py-2.5 rounded-lg cursor-pointer border transition-colors ${
                    highlighted
                      ? "bg-blue-50 border-blue-200 text-blue-900"
                      : selectedItem
                        ? "bg-green-50 border-green-200 text-green-900"
                        : "bg-white border-transparent hover:bg-slate-50"
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <Building2 className="h-4 w-4 text-slate-400" />
                    <div>
                      <div className="text-sm font-medium text-slate-900">{c.company_name}</div>
                      <div className="text-xs text-slate-500">
                        {c.nse_symbol || c.bse_code || ""} • {c.source_exchange}
                      </div>
                    </div>
                  </div>
                  {(highlighted || selectedItem) && <Check className="h-4 w-4 text-blue-600" />}
                </li>
                );
              })}
            </ul>

          {/* Actions */}
          <div className="flex items-center justify-between gap-3 p-3 border-t border-slate-200">
            <div className="text-xs text-slate-500">{selected.length} selected</div>
            <div className="flex items-center gap-2">
              <button
                type="button"
                className="px-3 py-1.5 text-slate-700 border border-slate-300 rounded-md hover:bg-slate-50 disabled:opacity-50"
                disabled={submitting}
                onClick={() => setSelected([])}
              >
                Clear
              </button>
              <button
                type="button"
                className="px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed inline-flex items-center gap-2"
                disabled={selected.length === 0 || submitting}
                onClick={async () => {
                  if (submitting) return;
                  setSubmitError(null);
                  setSubmitting(true);
                  try {
                    const targetWatchlistId = watchlistId || watchlists[0]?.id || defaultWatchlistId || "";
                    if (!targetWatchlistId || selected.length === 0) return;
                    const res = await onAddCompany(selected, targetWatchlistId);
                    if (res.success) {
                      setSelected([]);
                      setQuery("");
                      setSuggestions([]);
                      setOpen(false);
                      requestAnimationFrame(() => buttonRef.current?.focus());
                    } else {
                      setSubmitError(res.error || 'Failed to add');
                    }
                  } catch (e: any) {
                    setSubmitError(e?.message || 'Failed to add');
                  } finally {
                    setSubmitting(false);
                  }
                }}
              >
                {submitting && <Loader2 className="w-4 h-4 animate-spin" />} Add {selected.length > 0 ? `(${selected.length})` : ""}
              </button>
            </div>
          </div>

          {submitError && (
            <div className="px-3 pb-3 text-sm text-red-600">{submitError}</div>
          )}

        </div>, portalRef.current
      )}
    </div>
  );
}

