'use client';

import { useState } from 'react';
import { Mail, Lock, Trash2, AlertTriangle } from 'lucide-react';
import { usePasswordChange, useEmailChange, useAccountDeletion, useProfileValidation } from '@/lib/profile/hooks';
import { PasswordFormData, EmailFormData, PasswordFormErrors, EmailFormErrors } from '@/lib/profile/types';
import LoadingSpinner from '../LoadingSpinner';

interface AccountSettingsProps {
  currentEmail: string;
}

export default function AccountSettings({ currentEmail }: AccountSettingsProps) {
  const [activeSection, setActiveSection] = useState<'password' | 'email' | 'delete' | null>(null);
  
  // Password change state
  const [passwordData, setPasswordData] = useState<PasswordFormData>({
    current_password: '',
    new_password: '',
    confirm_password: ''
  });
  const [passwordErrors, setPasswordErrors] = useState<PasswordFormErrors>({});
  
  // Email change state
  const [emailData, setEmailData] = useState<EmailFormData>({
    new_email: '',
    password: ''
  });
  const [emailErrors, setEmailErrors] = useState<EmailFormErrors>({});
  
  // Account deletion state
  const [deleteConfirmation, setDeleteConfirmation] = useState('');
  const [deletePassword, setDeletePassword] = useState('');
  const [deleteError, setDeleteError] = useState('');

  const { changePassword, loading: passwordLoading, error: passwordError } = usePasswordChange();
  const { changeEmail, loading: emailLoading, error: emailError } = useEmailChange();
  const { deleteAccount, loading: deleteLoading, error: deleteAccountError } = useAccountDeletion();
  const { validatePassword, validateEmail } = useProfileValidation();

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validatePassword(passwordData);
    if (!validation.isValid) {
      setPasswordErrors(validation.errors);
      return;
    }

    const result = await changePassword(passwordData);
    if (result.success) {
      setPasswordData({ current_password: '', new_password: '', confirm_password: '' });
      setActiveSection(null);
      alert('Password changed successfully!');
    }
  };

  const handleEmailChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const errors: EmailFormErrors = {};
    
    if (!emailData.new_email) {
      errors.new_email = 'New email is required';
    } else if (!validateEmail(emailData.new_email)) {
      errors.new_email = 'Please enter a valid email address';
    }
    
    if (!emailData.password) {
      errors.password = 'Password is required to change email';
    }
    
    if (Object.keys(errors).length > 0) {
      setEmailErrors(errors);
      return;
    }

    const result = await changeEmail(emailData);
    if (result.success) {
      setEmailData({ new_email: '', password: '' });
      setActiveSection(null);
      alert('Email change request sent! Please check your new email for confirmation.');
    }
  };

  const handleAccountDeletion = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (deleteConfirmation !== 'DELETE') {
      setDeleteError('Please type "DELETE" to confirm');
      return;
    }
    
    if (!deletePassword) {
      setDeleteError('Password is required');
      return;
    }

    const result = await deleteAccount();
    
    if (result.success) {
      alert('Account deleted successfully');
      // User will be redirected by the auth system
    }
  };

  return (
    <div className="space-y-6">
      {/* Password Change */}
      <div className="bg-white border border-slate-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Lock className="w-5 h-5 text-slate-600" />
            <div>
              <h3 className="text-lg font-medium text-slate-900">Password</h3>
              <p className="text-sm text-slate-600">Change your account password</p>
            </div>
          </div>
          
          <button
            onClick={() => setActiveSection(activeSection === 'password' ? null : 'password')}
            className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700"
          >
            {activeSection === 'password' ? 'Cancel' : 'Change'}
          </button>
        </div>

        {activeSection === 'password' && (
          <form onSubmit={handlePasswordChange} className="space-y-4">
            {passwordError && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{passwordError.message}</p>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">
                Current Password
              </label>
              <input
                type="password"
                value={passwordData.current_password}
                onChange={(e) => setPasswordData(prev => ({ ...prev, current_password: e.target.value }))}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={passwordLoading}
              />
              {passwordErrors.current_password && (
                <p className="mt-1 text-sm text-red-600">{passwordErrors.current_password}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">
                New Password
              </label>
              <input
                type="password"
                value={passwordData.new_password}
                onChange={(e) => setPasswordData(prev => ({ ...prev, new_password: e.target.value }))}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={passwordLoading}
              />
              {passwordErrors.new_password && (
                <p className="mt-1 text-sm text-red-600">{passwordErrors.new_password}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">
                Confirm New Password
              </label>
              <input
                type="password"
                value={passwordData.confirm_password}
                onChange={(e) => setPasswordData(prev => ({ ...prev, confirm_password: e.target.value }))}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={passwordLoading}
              />
              {passwordErrors.confirm_password && (
                <p className="mt-1 text-sm text-red-600">{passwordErrors.confirm_password}</p>
              )}
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setActiveSection(null)}
                className="px-4 py-2 text-sm font-medium text-slate-700 border border-slate-300 rounded-lg hover:bg-slate-50"
                disabled={passwordLoading}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={passwordLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
              >
                {passwordLoading && <LoadingSpinner size="sm" />}
                <span>Change Password</span>
              </button>
            </div>
          </form>
        )}
      </div>

      {/* Email Change */}
      <div className="bg-white border border-slate-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Mail className="w-5 h-5 text-slate-600" />
            <div>
              <h3 className="text-lg font-medium text-slate-900">Email Address</h3>
              <p className="text-sm text-slate-600">Current: {currentEmail}</p>
            </div>
          </div>
          
          <button
            onClick={() => setActiveSection(activeSection === 'email' ? null : 'email')}
            className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700"
          >
            {activeSection === 'email' ? 'Cancel' : 'Change'}
          </button>
        </div>

        {activeSection === 'email' && (
          <form onSubmit={handleEmailChange} className="space-y-4">
            {emailError && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{emailError.message}</p>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">
                New Email Address
              </label>
              <input
                type="email"
                value={emailData.new_email}
                onChange={(e) => setEmailData(prev => ({ ...prev, new_email: e.target.value }))}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={emailLoading}
              />
              {emailErrors.new_email && (
                <p className="mt-1 text-sm text-red-600">{emailErrors.new_email}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">
                Current Password
              </label>
              <input
                type="password"
                value={emailData.password}
                onChange={(e) => setEmailData(prev => ({ ...prev, password: e.target.value }))}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={emailLoading}
              />
              {emailErrors.password && (
                <p className="mt-1 text-sm text-red-600">{emailErrors.password}</p>
              )}
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setActiveSection(null)}
                className="px-4 py-2 text-sm font-medium text-slate-700 border border-slate-300 rounded-lg hover:bg-slate-50"
                disabled={emailLoading}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={emailLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
              >
                {emailLoading && <LoadingSpinner size="sm" />}
                <span>Change Email</span>
              </button>
            </div>
          </form>
        )}
      </div>

      {/* Account Deletion */}
      <div className="bg-white border border-red-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Trash2 className="w-5 h-5 text-red-600" />
            <div>
              <h3 className="text-lg font-medium text-red-900">Delete Account</h3>
              <p className="text-sm text-red-600">Permanently delete your account and all data</p>
            </div>
          </div>
          
          <button
            onClick={() => setActiveSection(activeSection === 'delete' ? null : 'delete')}
            className="px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700"
          >
            {activeSection === 'delete' ? 'Cancel' : 'Delete'}
          </button>
        </div>

        {activeSection === 'delete' && (
          <form onSubmit={handleAccountDeletion} className="space-y-4">
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-red-800">Warning</h4>
                  <p className="text-sm text-red-700 mt-1">
                    This action cannot be undone. All your data including watchlists, preferences, and profile information will be permanently deleted.
                  </p>
                </div>
              </div>
            </div>

            {(deleteAccountError || deleteError) && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{deleteAccountError?.message || deleteError}</p>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">
                Type &quot;DELETE&quot; to confirm
              </label>
              <input
                type="text"
                value={deleteConfirmation}
                onChange={(e) => setDeleteConfirmation(e.target.value)}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                disabled={deleteLoading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">
                Enter your password
              </label>
              <input
                type="password"
                value={deletePassword}
                onChange={(e) => setDeletePassword(e.target.value)}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                disabled={deleteLoading}
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => {
                  setActiveSection(null);
                  setDeleteConfirmation('');
                  setDeletePassword('');
                  setDeleteError('');
                }}
                className="px-4 py-2 text-sm font-medium text-slate-700 border border-slate-300 rounded-lg hover:bg-slate-50"
                disabled={deleteLoading}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={deleteLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 disabled:opacity-50 flex items-center space-x-2"
              >
                {deleteLoading && <LoadingSpinner size="sm" />}
                <span>Delete Account</span>
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}
