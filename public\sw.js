// Lightweight service worker for static asset caching
self.addEventListener('install', (event) => {
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  self.clients.claim();
});

self.addEventListener('fetch', (event) => {
  const req = event.request;
  const url = new URL(req.url);

  // Cache-first for Next static assets and images
  const isStatic = url.pathname.startsWith('/_next/static/') || url.pathname.startsWith('/_next/image/');
  const isAsset = url.pathname.startsWith('/images/') || url.pathname.startsWith('/fonts/');

  if (req.method === 'GET' && (isStatic || isAsset)) {
    event.respondWith(
      caches.open('static-v1').then(async (cache) => {
        const cached = await cache.match(req);
        if (cached) return cached;
        try {
          const res = await fetch(req);
          if (res && res.ok) cache.put(req, res.clone());
          return res;
        } catch (e) {
          return cached || Response.error();
        }
      })
    );
  }
});

