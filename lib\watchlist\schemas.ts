import { z } from 'zod';

export const WatchlistSchema = z.object({
  id: z.string().uuid().optional(),
  user_id: z.string().uuid().optional(),
  name: z.string().min(1).max(120),
  description: z.string().max(500).optional().nullable(),
  is_default: z.boolean().optional().default(false),
  created_at: z.string().datetime().optional(),
  updated_at: z.string().datetime().optional(),
});

export type Watchlist = z.infer<typeof WatchlistSchema>;

export const WatchlistItemSchema = z.object({
  id: z.string().uuid().optional(),
  watchlist_id: z.string().uuid(),
  company_name: z.string().min(1),
  symbol: z.string().optional().nullable(),
  exchange: z.string().optional().nullable(),
  nse_symbol: z.string().optional().nullable(),
  bse_code: z.string().optional().nullable(),
  position: z.number().int().nonnegative().optional(),
  notes: z.string().optional().nullable(),
  added_at: z.string().datetime().optional(),
  updated_at: z.string().datetime().optional(),
});

export type WatchlistItem = z.infer<typeof WatchlistItemSchema>;

export const CreateWatchlistRequestSchema = WatchlistSchema.pick({ name: true, description: true, is_default: true });
export type CreateWatchlistRequest = z.infer<typeof CreateWatchlistRequestSchema>;

export const UpdateWatchlistRequestSchema = WatchlistSchema.pick({ name: true, description: true, is_default: true }).partial();
export type UpdateWatchlistRequest = z.infer<typeof UpdateWatchlistRequestSchema>;

export const AddWatchlistItemsRequestSchema = z.object({
  watchlist_id: z.string().uuid(),
  companies: z.array(z.object({
    company_name: z.string().min(1),
    nse_symbol: z.string().optional(),
    bse_code: z.string().optional(),
    exchange: z.string().optional(),
  })).min(1),
});
export type AddWatchlistItemsRequest = z.infer<typeof AddWatchlistItemsRequestSchema>;

export const ReorderItemsRequestSchema = z.object({
  watchlist_id: z.string().uuid(),
  ordered_ids: z.array(z.string().uuid()).min(1),
});
export type ReorderItemsRequest = z.infer<typeof ReorderItemsRequestSchema>;

