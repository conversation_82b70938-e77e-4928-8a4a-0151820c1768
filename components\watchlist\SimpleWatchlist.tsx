"use client";

import React, { useEffect, useState } from 'react';

// Minimal types
interface WatchlistItem {
  id: string;
  company_name: string;
  nse_symbol?: string;
  bse_code?: string;
  exchange?: string; // expected 'NSE' or 'BOM'
}

async function fetchWatchlistItemsREST(params: { watchlistId?: string; watchlistName?: string }): Promise<WatchlistItem[]> {
  // Use our server-side API that calls Supabase with service role to bypass RLS for now
  const url = new URL('/api/watchlist/items', window.location.origin);
  if (params.watchlistId) url.searchParams.set('watchlist_id', params.watchlistId);
  if (params.watchlistName) url.searchParams.set('watchlist_name', params.watchlistName);

  const resp = await fetch(url.toString(), { cache: 'no-store' });
  if (!resp.ok) return [];
  const data = await resp.json();
  return (data.items || []) as WatchlistItem[];
}

// Live number cells
function PriceCell({ id, exchange, initial }: { id: string; exchange: string; initial?: number }) {
  const [value, setValue] = useState<number | undefined>(initial);
  useEffect(() => {
    let mounted = true;
    (async () => {
      try {
        const resp = await fetch('/api/finance/yahoo-batch-quotes', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify([{ symbol: id, exchange }]),
        });
        if (resp.ok) {
          const [q] = await resp.json();
          const last = typeof q?.current_price === 'number' ? q.current_price : q?.last_price;
          if (mounted) setValue(last ?? initial);
        }
      } catch {}
    })();
    return () => { mounted = false; };
  }, [id, exchange, initial]);
  const disp = typeof value === 'number' ? value.toFixed(2) : '—';
  return <span className="font-medium tabular-nums">{disp}</span>;
}

function ChangeCell({ id, exchange, initialChange, initialPercent }: { id: string; exchange: string; initialChange?: number; initialPercent?: number }) {
  const [chg, setChg] = useState<number | undefined>(initialChange);
  const [pct, setPct] = useState<number | undefined>(initialPercent);
  const isPos = (chg ?? 0) >= 0;
  useEffect(() => {
    let mounted = true;
    (async () => {
      try {
        const resp = await fetch('/api/finance/yahoo-batch-quotes', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify([{ symbol: id, exchange }]),
        });
        if (resp.ok) {
          const [q] = await resp.json();
          if (mounted) {
            const ch = typeof q?.change_amount === 'number' ? q.change_amount : q?.change;
            const pctVal = typeof q?.change_percentage === 'number' ? q.change_percentage : q?.change_percent;
            setChg(ch ?? initialChange);
            setPct(pctVal ?? initialPercent);
          }
        }
      } catch {}
    })();
    return () => { mounted = false; };
  }, [id, exchange, initialChange, initialPercent]);
  return (
    <div className="flex flex-col items-end">
      <span className={`font-medium ${isPos ? 'text-green-600' : 'text-red-600'}`}>{(chg ?? 0) >= 0 ? `+${(chg ?? 0).toFixed(2)}` : (chg ?? 0).toFixed(2)}</span>
      <span className={`text-xs ${isPos ? 'text-green-600' : 'text-red-600'}`}>{(pct ?? 0) >= 0 ? `+${(pct ?? 0).toFixed(2)}%` : `${(pct ?? 0).toFixed(2)}%`}</span>
    </div>
  );
}

export default function SimpleWatchlist({ watchlistId, watchlistName }: { watchlistId?: string; watchlistName?: string }) {
  const [items, setItems] = useState<WatchlistItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;
    (async () => {
      setLoading(true);
      setError(null);
      try {
        const rows = await fetchWatchlistItemsREST({ watchlistId, watchlistName });
        if (mounted) setItems(rows);
      } catch (e: any) {
        if (mounted) setError(e?.message || 'Failed to fetch');
      } finally {
        if (mounted) setLoading(false);
      }
    })();
    return () => { mounted = false; };
  }, [watchlistId, watchlistName]);

  if (loading) {
    return <div className="flex justify-center items-center py-12">Loading...</div>;
  }
  if (error) {
    return (
      <div className="flex flex-col items-center py-12">
        <div className="text-red-600 mb-2">{error}</div>
        <button className="px-3 py-2 bg-blue-600 text-white rounded" onClick={() => location.reload()}>Reload</button>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full table-fixed">
        <thead>
          <tr className="border-b">
            <th className="text-left px-4 py-2">Company</th>
            <th className="text-right px-4 py-2">Last Price</th>
            <th className="text-right px-4 py-2">Change</th>
          </tr>
        </thead>
        <tbody>
          {items.map((it) => {
            const ex = (it.exchange || (it.nse_symbol ? 'NSE' : it.bse_code ? 'BOM' : 'NSE')).toUpperCase() === 'BSE' ? 'BOM' : (it.exchange || 'NSE');
            const id = it.nse_symbol || it.bse_code || '';
            return (
              <tr key={it.id} className="border-b">
                <td className="px-4 py-2 text-sm">
                  <div className="flex flex-col">
                    <span className="font-medium text-slate-900">{it.company_name}</span>
                    <span className="text-xs text-slate-500">{it.nse_symbol || it.bse_code || '—'}</span>
                  </div>
                </td>
                <td className="px-4 py-2 text-right">
                  <PriceCell id={id} exchange={ex} />
                </td>
                <td className="px-4 py-2 text-right">
                  <ChangeCell id={id} exchange={ex} />
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}

