'use client';

import { useState, useRef } from 'react';
import { Camera, Upload, X, User } from 'lucide-react';
import Image from 'next/image';
import { AvatarUploadProps } from '@/lib/profile/types';
import LoadingSpinner from '../LoadingSpinner';

export default function AvatarUpload({
  currentAvatarUrl,
  onUpload,
  loading = false
}: AvatarUploadProps) {
  const [dragOver, setDragOver] = useState(false);
  const [preview, setPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    // Validate file
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB');
      return;
    }

    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      alert('Only JPEG, PNG, and WebP files are allowed');
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Upload file
    onUpload(file);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const clearPreview = () => {
    setPreview(null);
  };

  const displayUrl = preview || currentAvatarUrl;

  return (
    <div className="flex flex-col items-center space-y-4">
      {/* Avatar Display */}
      <div className="relative">
        <div
          className={`
            relative w-32 h-32 rounded-full border-4 border-slate-200 overflow-hidden
            ${dragOver ? 'border-blue-500 bg-blue-50' : ''}
            ${loading ? 'opacity-50' : ''}
          `}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          {displayUrl ? (
            <Image
              src={displayUrl}
              alt="Profile avatar"
              width={128}
              height={128}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-slate-100 flex items-center justify-center">
              <User className="w-12 h-12 text-slate-400" />
            </div>
          )}

          {/* Loading Overlay */}
          {loading && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <LoadingSpinner size="sm" />
            </div>
          )}

          {/* Drag Overlay */}
          {dragOver && !loading && (
            <div className="absolute inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center">
              <Upload className="w-8 h-8 text-blue-600" />
            </div>
          )}
        </div>

        {/* Camera Button */}
        <button
          onClick={handleClick}
          disabled={loading}
          className="absolute bottom-0 right-0 w-10 h-10 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors"
        >
          <Camera className="w-5 h-5" />
        </button>

        {/* Clear Preview Button */}
        {preview && (
          <button
            onClick={clearPreview}
            className="absolute top-0 right-0 w-8 h-8 bg-red-500 text-white rounded-full shadow-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 flex items-center justify-center transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Upload Instructions */}
      <div className="text-center">
        <button
          onClick={handleClick}
          disabled={loading}
          className="text-blue-600 hover:text-blue-700 font-medium text-sm disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {currentAvatarUrl ? 'Change Avatar' : 'Upload Avatar'}
        </button>
        <p className="text-xs text-slate-500 mt-1">
          JPG, PNG or WebP. Max size 5MB.
        </p>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/png,image/webp"
        onChange={handleFileInputChange}
        className="hidden"
        disabled={loading}
      />
    </div>
  );
}
