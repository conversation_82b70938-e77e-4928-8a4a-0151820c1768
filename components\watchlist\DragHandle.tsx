'use client';

import { GripVertical } from 'lucide-react';

interface DragHandleProps extends React.HTMLAttributes<HTMLDivElement> {
  isDragging?: boolean;
  className?: string;
}

export default function DragHandle({ isDragging = false, className = '', ...rest }: DragHandleProps) {
  return (
    <div
      {...rest}
      className={`
        flex items-center justify-center w-6 h-6 cursor-grab active:cursor-grabbing
        text-slate-400 hover:text-slate-600 transition-colors
        ${isDragging ? 'text-blue-600' : ''}
        ${className}
      `}
      title="Drag to reorder"
    >
      <GripVertical className="w-4 h-4" />
    </div>
  );
}
