'use client';

import React, { useState } from 'react';
import DocumentAnalysisButton from '@/components/DocumentAnalysisButton';
import { DocumentAnalysisResult } from '@/lib/documentAnalysisService';

export default function TestAnalysisPage() {
  const [testUrl, setTestUrl] = useState('');
  const [analysisResults, setAnalysisResults] = useState<DocumentAnalysisResult[]>([]);

  const handleAnalysisComplete = (result: DocumentAnalysisResult) => {
    setAnalysisResults(prev => [result, ...prev]);
  };

  const sampleUrls = [
    'https://nsearchives.nseindia.com/corporate/MAHLOG18_21072025134030_OutcomeofBMQ1FY26Signed.pdf',
    'https://nsearchives.nseindia.com/corporate/RATEGAIN_21072025134030_OutcomeofBMQ1FY26Signed.pdf',
    'https://nsearchives.nseindia.com/corporate/SBICARDS_22072025134030_OutcomeofBMQ1FY26Signed.pdf',
  ];

  return (
    <div className="min-h-screen bg-slate-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-xl shadow-sm p-6 mb-8">
          <h1 className="text-2xl font-bold text-slate-900 mb-2">
            AI SUMMARIZER: Complete Document Analysis
          </h1>
          <p className="text-slate-600 mb-6">
            Test the new AI Summarizer system that processes complete documents with comprehensive extraction and structured output
          </p>
          
          <div className="space-y-6">
            {/* URL Input */}
            <div>
              <label htmlFor="testUrl" className="block text-sm font-medium text-slate-700 mb-2">
                PDF URL to Analyze
              </label>
              <input
                id="testUrl"
                type="url"
                value={testUrl}
                onChange={(e) => setTestUrl(e.target.value)}
                placeholder="Enter PDF URL..."
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Sample URLs */}
            <div>
              <p className="text-sm font-medium text-slate-700 mb-2">Sample URLs:</p>
              <div className="space-y-2">
                {sampleUrls.map((url, index) => (
                  <button
                    key={index}
                    onClick={() => setTestUrl(url)}
                    className="block w-full text-left px-3 py-2 text-sm bg-slate-50 hover:bg-slate-100 rounded border text-slate-600 hover:text-slate-800 transition-colors"
                  >
                    {url}
                  </button>
                ))}
              </div>
            </div>

            {/* Analysis Button */}
            <div>
              <DocumentAnalysisButton
                pdfUrl={testUrl}
                onAnalysisComplete={handleAnalysisComplete}
              />
            </div>
          </div>
        </div>

        {/* Analysis Results */}
        {analysisResults.length > 0 && (
          <div className="space-y-6">
            <h2 className="text-xl font-bold text-slate-900">Analysis Results</h2>
            {analysisResults.map((result, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm p-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-slate-900">
                      Analysis #{analysisResults.length - index}
                    </h3>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      result.status === 'success' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {result.status}
                    </span>
                  </div>

                  {result.status === 'success' ? (
                    <>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <p className="text-sm font-medium text-slate-700">Document Type</p>
                          <p className="text-slate-900">{result.document_type}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-slate-700">Company</p>
                          <p className="text-slate-900">{result.company_name}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-slate-700">Processing Method</p>
                          <p className="text-slate-900 font-mono text-xs bg-blue-100 px-2 py-1 rounded">
                            {(result as any).processed_with || 'ai_summarizer_v1.0.0'}
                          </p>
                        </div>
                      </div>

                      {/* Document Classification */}
                      {(result as any).document_classification && (
                        <div>
                          <p className="text-sm font-medium text-slate-700 mb-2">AI SUMMARIZER: Document Classification</p>
                          <div className="bg-blue-50 rounded-lg p-3">
                            <div className="flex flex-wrap gap-2 mb-2">
                              {((result as any).categories || []).map((category: string, idx: number) => (
                                <span key={idx} className="px-2 py-1 bg-blue-200 text-blue-800 text-xs rounded">
                                  {category}
                                </span>
                              ))}
                            </div>
                            <p className="text-xs text-blue-700">
                              Primary: <strong>{(result as any).primary_category}</strong>
                            </p>
                          </div>
                        </div>
                      )}

                      {/* Company Information */}
                      {(result as any).company_information && (
                        <div>
                          <p className="text-sm font-medium text-slate-700 mb-2">AI SUMMARIZER: Company Information</p>
                          <div className="bg-green-50 rounded-lg p-3 grid grid-cols-2 gap-2 text-xs">
                            <div>
                              <span className="font-medium text-green-700">Filing Date:</span>
                              <p className="text-green-900">{(result as any).company_information.filing_date}</p>
                            </div>
                            <div>
                              <span className="font-medium text-green-700">Quarter:</span>
                              <p className="text-green-900">{(result as any).company_information.quarter || 'N/A'}</p>
                            </div>
                            {(result as any).company_information.stock_exchanges && (
                              <div className="col-span-2">
                                <span className="font-medium text-green-700">Exchanges:</span>
                                <div className="flex gap-1 mt-1">
                                  {(result as any).company_information.stock_exchanges.map((exchange: string, idx: number) => (
                                    <span key={idx} className="px-1 py-0.5 bg-green-200 text-green-800 text-xs rounded">
                                      {exchange}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {result.summary && (
                        <div>
                          <p className="text-sm font-medium text-slate-700 mb-2">AI Summary</p>
                          <p className="text-slate-900 leading-relaxed bg-slate-50 p-3 rounded-lg">{result.summary}</p>
                        </div>
                      )}

                      {/* Financial Metrics for Quarterly Results */}
                      {(result as any).extracted_data?.quarterly_results && (
                        <div>
                          <p className="text-sm font-medium text-slate-700 mb-2">AI SUMMARIZER: Financial Metrics</p>
                          <div className="bg-blue-50 rounded-lg p-3 space-y-2">
                            {(() => {
                              const qrData = (result as any).extracted_data.quarterly_results;
                              return (
                                <>
                                  {qrData.revenue && (
                                    <div className="flex justify-between items-center text-sm">
                                      <span className="font-medium text-blue-700">Revenue:</span>
                                      <div className="text-right">
                                        <span className="text-blue-900">{qrData.revenue.current_quarter}</span>
                                        {qrData.revenue.yoy_growth_percentage && (
                                          <span className={`ml-2 text-xs ${qrData.revenue.yoy_growth_percentage.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                                            ({qrData.revenue.yoy_growth_percentage} YoY)
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  )}
                                  {qrData.ebitda && (
                                    <div className="flex justify-between items-center text-sm">
                                      <span className="font-medium text-blue-700">EBITDA:</span>
                                      <div className="text-right">
                                        <span className="text-blue-900">{qrData.ebitda.current_quarter}</span>
                                        {qrData.ebitda.yoy_growth_percentage && (
                                          <span className={`ml-2 text-xs ${qrData.ebitda.yoy_growth_percentage.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                                            ({qrData.ebitda.yoy_growth_percentage} YoY)
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  )}
                                  {qrData.pat && (
                                    <div className="flex justify-between items-center text-sm">
                                      <span className="font-medium text-blue-700">PAT:</span>
                                      <div className="text-right">
                                        <span className="text-blue-900">{qrData.pat.current_quarter}</span>
                                        {qrData.pat.yoy_growth_percentage && (
                                          <span className={`ml-2 text-xs ${qrData.pat.yoy_growth_percentage.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                                            ({qrData.pat.yoy_growth_percentage} YoY)
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  )}
                                </>
                              );
                            })()}
                          </div>
                        </div>
                      )}

                      {/* Investment Implications */}
                      {(result as any).investment_implications && (result as any).investment_implications.length > 0 && (
                        <div>
                          <p className="text-sm font-medium text-slate-700 mb-2">AI SUMMARIZER: Investment Implications</p>
                          <div className="bg-yellow-50 rounded-lg p-3">
                            <ul className="space-y-1">
                              {((result as any).investment_implications || []).map((implication: string, idx: number) => (
                                <li key={idx} className="text-sm text-yellow-800 flex items-start gap-2">
                                  <span className="w-1 h-1 bg-yellow-600 rounded-full mt-2 flex-shrink-0"></span>
                                  {implication}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}

                      <div className="text-xs text-slate-500 bg-slate-50 p-2 rounded">
                        <p>Analysis Date: {new Date(result.analysis_date).toLocaleString()}</p>
                        <p>Source: {result.source_url}</p>
                      </div>
                    </>
                  ) : (
                    <div className="text-red-600">
                      <p className="font-medium">Error:</p>
                      <p>{result.error_message}</p>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
