export interface DocumentAnalysisResult {
  document_type: string;
  company_name: string;
  summary: string;
  key_details: Record<string, unknown>;
  analysis_date: string;
  source_url: string;
  status: 'success' | 'error';
  error_message?: string;
  // NEW METHOD fields
  processed_with?: string;
  complete_analysis?: any;
  document_classification?: {
    categories: string[];
    confidence_scores: Record<string, number>;
    primary_category: string;
  };
  company_information?: {
    company_name: string;
    filing_date: string;
    quarter?: string;
    stock_exchanges?: string[];
    scrip_codes?: Record<string, string>;
  };
  extracted_data?: Record<string, any>;
  investment_implications?: string[];
  categories?: string[];
  primary_category?: string;
}

export async function analyzeDocument(pdfUrl: string): Promise<DocumentAnalysisResult> {
  try {
    const response = await fetch('/api/analyze-document', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ pdfUrl }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to analyze document');
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Document analysis error:', error);
    throw error;
  }
}
