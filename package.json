{"name": "corporate_filings", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:hybrid": "jest --testPathPattern=hybrid-architecture", "test:auth": "jest --testPathPattern=auth-error-handling", "check-config": "node scripts/check-production-config.js", "validate-supabase": "node scripts/validate-supabase-config.js", "build:production": "node scripts/check-production-config.js && next build", "analyze": "ANALYZE=true next build", "analyze:server": "BUNDLE_ANALYZE=server next build", "analyze:browser": "BUNDLE_ANALYZE=browser next build"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.52.0", "@tanstack/react-query": "^5.84.2", "@tanstack/react-query-devtools": "^5.85.6", "critters": "^0.0.23", "lucide-react": "^0.525.0", "next": "^15.4.1", "react": "^19.1.0", "react-dom": "^19.1.0", "web-vitals": "^5.1.0", "yahoo-finance2": "^2.13.3", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^17.2.1", "eslint": "^9", "eslint-config-next": "15.4.1", "puppeteer": "^24.16.1", "tailwindcss": "^4", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.2"}}