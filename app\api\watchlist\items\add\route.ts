import { NextRequest } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { AddWatchlistItemsRequestSchema } from '@/lib/watchlist/schemas';

export async function POST(req: NextRequest) {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (!supabaseUrl || !serviceKey) {
      return new Response(JSON.stringify({ error: 'Missing Supabase env' }), { status: 500, headers: { 'content-type': 'application/json' } });
    }
    const supabase = createClient(supabaseUrl, serviceKey);

    const json = await req.json();
    const parsed = AddWatchlistItemsRequestSchema.safeParse(json);
    if (!parsed.success) {
      return new Response(JSON.stringify({ error: parsed.error.flatten() }), { status: 400, headers: { 'content-type': 'application/json' } });
    }
    const { watchlist_id, companies } = parsed.data;

    // Determine next position
    const { data: maxPosData } = await supabase
      .from('watchlist_items')
      .select('position')
      .eq('watchlist_id', watchlist_id)
      .order('position', { ascending: false })
      .limit(1);
    const startPos = (maxPosData && maxPosData[0]?.position) ? (maxPosData[0].position + 1) : 1;

    // De-duplicate by existing keys to avoid constraint errors and speed up insert
    const norm = (v?: string) => String(v || '').toLowerCase().trim();
    const { data: existing } = await supabase
      .from('watchlist_items')
      .select('company_name,nse_symbol,bse_code')
      .eq('watchlist_id', watchlist_id);
    const existingSet = new Set<string>((existing || []).flatMap((it) => [it.company_name, it.nse_symbol, it.bse_code]).filter(Boolean).map(norm));

    const deduped = companies.filter((c) => {
      const keys = [c.company_name, c.nse_symbol, c.bse_code].filter(Boolean).map(norm);
      return !keys.some(k => existingSet.has(k));
    });

    const rows = deduped.map((c, i) => {
      const exchange = c.exchange || (c.nse_symbol ? 'NSE' : c.bse_code ? 'BOM' : null);
      const symbol = c.nse_symbol ? c.nse_symbol : (c.bse_code ? c.bse_code : null);
      return {
        watchlist_id,
        company_name: c.company_name,
        symbol,
        nse_symbol: c.nse_symbol || null,
        bse_code: c.bse_code || null,
        exchange,
        position: startPos + i,
      };
    });

    if (rows.length === 0) {
      return new Response(JSON.stringify({ items: [], skipped: companies.length, reason: 'duplicates' }), { status: 409, headers: { 'content-type': 'application/json' } });
    }

    const { data, error } = await supabase
      .from('watchlist_items')
      .insert(rows)
      .select('id,company_name,nse_symbol,bse_code,exchange,position');
    if (error) throw error;

    return new Response(JSON.stringify({ items: data }), { status: 200, headers: { 'content-type': 'application/json' } });
  } catch (e: any) {
    return new Response(JSON.stringify({ error: e?.message || 'Server error' }), { status: 500, headers: { 'content-type': 'application/json' } });
  }
}

