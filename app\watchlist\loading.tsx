import LoadingSpinner from '@/components/LoadingSpinner';

export default function WatchlistLoading() {
  return (
    <div className="min-h-screen bg-slate-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Skeleton */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <div className="h-8 w-32 bg-slate-200 rounded animate-pulse mb-2" />
              <div className="h-4 w-48 bg-slate-200 rounded animate-pulse" />
            </div>
            <div className="flex items-center space-x-3">
              <div className="h-10 w-32 bg-slate-200 rounded animate-pulse" />
              <div className="h-10 w-32 bg-slate-200 rounded animate-pulse" />
            </div>
          </div>

          {/* Tabs Skeleton */}
          <div className="flex items-center space-x-1 border-b border-slate-200">
            <div className="h-12 w-32 bg-slate-200 rounded-t animate-pulse" />
            <div className="h-12 w-32 bg-slate-200 rounded-t animate-pulse" />
          </div>
        </div>

        {/* Toolbar Skeleton */}
        <div className="bg-white rounded-lg border border-slate-200 p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="h-10 w-64 bg-slate-200 rounded animate-pulse" />
              <div className="h-10 w-20 bg-slate-200 rounded animate-pulse" />
            </div>
            <div className="flex items-center space-x-3">
              <div className="h-10 w-20 bg-slate-200 rounded animate-pulse" />
              <div className="h-4 w-24 bg-slate-200 rounded animate-pulse" />
            </div>
          </div>
        </div>

        {/* Table Skeleton */}
        <div className="bg-white rounded-lg border border-slate-200 overflow-hidden">
          <div className="p-4">
            <LoadingSpinner />
            <p className="text-center text-slate-500 mt-4">Loading watchlist...</p>
          </div>
        </div>
      </div>
    </div>
  );
}
