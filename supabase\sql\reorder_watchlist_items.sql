-- Transactional reorder of watchlist items by ordered_ids
-- Usage: select reorder_watchlist_items(_watchlist_id := '...', _ordered_ids := array['id1','id2',...]);

create or replace function public.reorder_watchlist_items(_watchlist_id uuid, _ordered_ids uuid[])
returns void
language plpgsql
security definer
as $$
begin
  perform set_config('role', current_user, true); -- no-op, placeholder
  if _watchlist_id is null or _ordered_ids is null then
    raise exception 'Invalid input';
  end if;

  -- Wrap in a transaction
  perform pg_advisory_xact_lock(hashtext(_watchlist_id::text));

  -- Update positions in order
  for i in 1..array_length(_ordered_ids, 1) loop
    update public.watchlist_items
    set position = i
    where watchlist_id = _watchlist_id and id = _ordered_ids[i];
  end loop;
end;
$$;

-- Optional: grant to anon/authenticated if using RLS+RPC
-- grant execute on function public.reorder_watchlist_items(uuid, uuid[]) to anon, authenticated;

