// Performance testing script to validate optimizations
const puppeteer = require('puppeteer');

async function measurePerformance(url, testName) {
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();
  
  // Enable performance monitoring
  await page.setCacheEnabled(false); // Disable cache for accurate measurements
  
  console.log(`\n🧪 Testing: ${testName}`);
  console.log(`📍 URL: ${url}`);
  
  const startTime = Date.now();
  
  // Navigate to page and wait for load
  await page.goto(url, { waitUntil: 'networkidle0' });
  
  const loadTime = Date.now() - startTime;
  
  // Get performance metrics
  const metrics = await page.evaluate(() => {
    const navigation = performance.getEntriesByType('navigation')[0];
    const paint = performance.getEntriesByType('paint');
    
    return {
      // Navigation timing
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      
      // Paint timing
      firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
      firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
      
      // Resource timing
      totalResources: performance.getEntriesByType('resource').length,
      totalTransferSize: performance.getEntriesByType('resource')
        .reduce((total, resource) => total + (resource.transferSize || 0), 0),
    };
  });
  
  // Get Web Vitals if available
  const webVitals = await page.evaluate(() => {
    return new Promise((resolve) => {
      const vitals = {};
      let count = 0;
      const maxWait = 3000; // 3 seconds
      
      const checkComplete = () => {
        if (count >= 3 || Date.now() - startTime > maxWait) {
          resolve(vitals);
        }
      };
      
      const startTime = Date.now();
      
      // Try to get CLS
      if (window.webVitals) {
        window.webVitals.getCLS((metric) => {
          vitals.cls = metric.value;
          count++;
          checkComplete();
        });
        
        window.webVitals.getLCP((metric) => {
          vitals.lcp = metric.value;
          count++;
          checkComplete();
        });
        
        window.webVitals.getFCP((metric) => {
          vitals.fcp = metric.value;
          count++;
          checkComplete();
        });
      }
      
      setTimeout(() => resolve(vitals), maxWait);
    });
  });
  
  await browser.close();
  
  // Display results
  console.log(`\n📊 Performance Results for ${testName}:`);
  console.log(`⏱️  Total Load Time: ${loadTime}ms`);
  console.log(`🎨 First Paint: ${metrics.firstPaint.toFixed(2)}ms`);
  console.log(`🖼️  First Contentful Paint: ${metrics.firstContentfulPaint.toFixed(2)}ms`);
  console.log(`📄 DOM Content Loaded: ${metrics.domContentLoaded.toFixed(2)}ms`);
  console.log(`✅ Load Complete: ${metrics.loadComplete.toFixed(2)}ms`);
  console.log(`📦 Total Resources: ${metrics.totalResources}`);
  console.log(`📊 Total Transfer Size: ${(metrics.totalTransferSize / 1024).toFixed(2)} KB`);
  
  if (webVitals.fcp) console.log(`🎯 FCP (Web Vitals): ${webVitals.fcp.toFixed(2)}ms`);
  if (webVitals.lcp) console.log(`🎯 LCP (Web Vitals): ${webVitals.lcp.toFixed(2)}ms`);
  if (webVitals.cls) console.log(`🎯 CLS (Web Vitals): ${webVitals.cls.toFixed(4)}`);
  
  return {
    testName,
    loadTime,
    ...metrics,
    ...webVitals
  };
}

async function runPerformanceTests() {
  console.log('🚀 Starting Performance Validation Tests\n');
  
  const tests = [
    { url: 'http://localhost:3000', name: 'Main Page (Optimized)' },
    { url: 'http://localhost:3000/watchlist', name: 'Watchlist Page (Lazy Loaded)' },
    { url: 'http://localhost:3000/profile', name: 'Profile Page' },
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      const result = await measurePerformance(test.url, test.name);
      results.push(result);
      
      // Wait between tests
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.error(`❌ Error testing ${test.name}:`, error.message);
    }
  }
  
  // Summary
  console.log('\n📈 Performance Summary:');
  console.log('=' .repeat(60));
  
  results.forEach(result => {
    console.log(`\n${result.testName}:`);
    console.log(`  Load Time: ${result.loadTime}ms`);
    console.log(`  FCP: ${result.firstContentfulPaint.toFixed(2)}ms`);
    console.log(`  Transfer Size: ${(result.totalTransferSize / 1024).toFixed(2)} KB`);
    
    // Performance assessment
    const assessment = result.loadTime < 1000 ? '🟢 Excellent' : 
                      result.loadTime < 2000 ? '🟡 Good' : '🔴 Needs Improvement';
    console.log(`  Assessment: ${assessment}`);
  });
  
  console.log('\n✅ Performance validation complete!');
}

// Run if called directly
if (require.main === module) {
  runPerformanceTests().catch(console.error);
}

module.exports = { measurePerformance, runPerformanceTests };
