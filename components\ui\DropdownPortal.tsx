'use client';

import { useEffect, useRef, useState, ReactNode } from 'react';
import { createPortal } from 'react-dom';

interface DropdownPortalProps {
  isOpen: boolean;
  onClose: () => void;
  triggerRef: React.RefObject<HTMLElement | null>;
  children: ReactNode;
  className?: string;
  placement?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
  offset?: { x: number; y: number };
}

export default function DropdownPortal({
  isOpen,
  onClose,
  triggerRef,
  children,
  className = '',
  placement = 'bottom-right',
  offset = { x: 0, y: 4 }
}: DropdownPortalProps) {
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Create portal container on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setPortalContainer(document.body);
    }
  }, []);

  // Calculate position when dropdown opens or window resizes
  useEffect(() => {
    if (!isOpen || !triggerRef.current || !portalContainer) return;

    const calculatePosition = () => {
      const triggerRect = triggerRef.current!.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      let top = 0;
      let left = 0;

      // Calculate base position based on placement
      switch (placement) {
        case 'bottom-left':
          top = triggerRect.bottom + offset.y;
          left = triggerRect.left + offset.x;
          break;
        case 'bottom-right':
          top = triggerRect.bottom + offset.y;
          left = triggerRect.right + offset.x;
          break;
        case 'top-left':
          top = triggerRect.top - offset.y;
          left = triggerRect.left + offset.x;
          break;
        case 'top-right':
          top = triggerRect.top - offset.y;
          left = triggerRect.right + offset.x;
          break;
      }

      // Get dropdown dimensions (estimate if not rendered yet)
      const dropdownWidth = dropdownRef.current?.offsetWidth || 200;
      const dropdownHeight = dropdownRef.current?.offsetHeight || 100;

      // Adjust for viewport boundaries
      if (left + dropdownWidth > viewportWidth) {
        left = viewportWidth - dropdownWidth - 8; // 8px margin from edge
      }
      if (left < 8) {
        left = 8;
      }

      if (top + dropdownHeight > viewportHeight) {
        // Flip to top if there's more space
        if (triggerRect.top > viewportHeight - triggerRect.bottom) {
          top = triggerRect.top - dropdownHeight - offset.y;
        } else {
          top = viewportHeight - dropdownHeight - 8;
        }
      }
      if (top < 8) {
        top = 8;
      }

      setPosition({ top, left });
    };

    calculatePosition();

    // Recalculate on window resize or scroll
    const handleResize = () => calculatePosition();
    const handleScroll = () => calculatePosition();

    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleScroll, true);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll, true);
    };
  }, [isOpen, triggerRef, portalContainer, placement, offset]);

  // Handle click outside to close
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      
      // Don't close if clicking on the trigger or dropdown
      if (
        triggerRef.current?.contains(target) ||
        dropdownRef.current?.contains(target)
      ) {
        return;
      }
      
      onClose();
    };

    // Use capture phase to ensure we catch the event before other handlers
    document.addEventListener('mousedown', handleClickOutside, true);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
    };
  }, [isOpen, onClose, triggerRef]);

  // Handle escape key
  useEffect(() => {
    if (!isOpen) return;

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen || !portalContainer) {
    return null;
  }

  return createPortal(
    <>
      {/* Backdrop for mobile/touch devices */}
      <div 
        className="fixed inset-0 z-40 bg-transparent"
        onClick={onClose}
      />
      
      {/* Dropdown content */}
      <div
        ref={dropdownRef}
        className={`fixed z-50 bg-white rounded-lg shadow-lg border border-slate-200 py-1 min-w-32 ${className}`}
        style={{
          top: `${position.top}px`,
          left: `${position.left}px`,
        }}
      >
        {children}
      </div>
    </>,
    portalContainer
  );
}
