import { NextRequest, NextResponse } from 'next/server';
import { fetchMasterCompanies } from '@/lib/supabase';

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const query = searchParams.get('q') || '';

  try {
    const results = await fetchMasterCompanies(query, 10);
    
    return NextResponse.json({
      query,
      count: results.length,
      results: results.map((company, index) => ({
        rank: index + 1,
        name: company.company_name,
        nse: company.nse_symbol,
        bse: company.bse_code,
        exchange: company.source_exchange
      }))
    });
  } catch {
    return NextResponse.json({ error: 'Search failed' }, { status: 500 });
  }
}
