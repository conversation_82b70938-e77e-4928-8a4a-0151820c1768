'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  UserProfile,
  UpdateProfileRequest,
  UpdatePreferencesRequest,
  ChangePasswordRequest,
  ChangeEmailRequest,

  AccountStats,
  ProfileError
} from './types';
import {
  getUserProfile,
  updateUserProfile,
  updateUserPreferences,
  uploadAvatar,
  changePassword,
  changeEmail,
  deleteAccount,
  getAccountStats,
  updateLastSeen
} from './api';

// Hook for managing user profile
export function useUserProfile() {
  const { user } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<ProfileError | null>(null);

  const fetchProfile = useCallback(async () => {
    if (!user) {
      setProfile(null);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const { data, error: fetchError } = await getUserProfile();
    
    if (fetchError) {
      setError(fetchError);
    } else {
      setProfile(data);
    }
    
    setLoading(false);
  }, [user]);

  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  const updateProfile = useCallback(async (updates: UpdateProfileRequest) => {
    const { data: updatedProfile, error } = await updateUserProfile(updates);
    
    if (error) {
      setError(error);
      return { success: false, error };
    }
    
    if (updatedProfile) {
      setProfile(updatedProfile);
    }
    
    return { success: true, data: updatedProfile };
  }, []);

  const updatePreferences = useCallback(async (updates: UpdatePreferencesRequest) => {
    const { data: updatedPreferences, error } = await updateUserPreferences(updates);
    
    if (error) {
      setError(error);
      return { success: false, error };
    }
    
    if (updatedPreferences && profile) {
      setProfile({
        ...profile,
        preferences: updatedPreferences
      });
    }
    
    return { success: true, data: updatedPreferences };
  }, [profile]);

  const uploadProfileAvatar = useCallback(async (file: File) => {
    const { data, error } = await uploadAvatar(file);
    
    if (error) {
      setError(error);
      return { success: false, error };
    }
    
    if (data && profile) {
      setProfile({
        ...profile,
        avatar_url: data.avatar_url
      });
    }
    
    return { success: true, data };
  }, [profile]);

  return {
    profile,
    loading,
    error,
    updateProfile,
    updatePreferences,
    uploadAvatar: uploadProfileAvatar,
    refetch: fetchProfile,
    clearError: () => setError(null)
  };
}

// Hook for password management
export function usePasswordChange() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ProfileError | null>(null);

  const changeUserPassword = useCallback(async (data: ChangePasswordRequest) => {
    setLoading(true);
    setError(null);

    const { error: changeError } = await changePassword(data);
    
    if (changeError) {
      setError(changeError);
      setLoading(false);
      return { success: false, error: changeError };
    }
    
    setLoading(false);
    return { success: true };
  }, []);

  return {
    loading,
    error,
    changePassword: changeUserPassword,
    clearError: () => setError(null)
  };
}

// Hook for email management
export function useEmailChange() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ProfileError | null>(null);

  const changeUserEmail = useCallback(async (data: ChangeEmailRequest) => {
    setLoading(true);
    setError(null);

    const { error: changeError } = await changeEmail(data);
    
    if (changeError) {
      setError(changeError);
      setLoading(false);
      return { success: false, error: changeError };
    }
    
    setLoading(false);
    return { success: true };
  }, []);

  return {
    loading,
    error,
    changeEmail: changeUserEmail,
    clearError: () => setError(null)
  };
}

// Hook for account deletion
export function useAccountDeletion() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ProfileError | null>(null);

  const deleteUserAccount = useCallback(async () => {
    setLoading(true);
    setError(null);

    const { error: deleteError } = await deleteAccount();
    
    if (deleteError) {
      setError(deleteError);
      setLoading(false);
      return { success: false, error: deleteError };
    }
    
    setLoading(false);
    return { success: true };
  }, []);

  return {
    loading,
    error,
    deleteAccount: deleteUserAccount,
    clearError: () => setError(null)
  };
}

// Hook for account statistics
export function useAccountStats() {
  const { user } = useAuth();
  const [stats, setStats] = useState<AccountStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<ProfileError | null>(null);

  const fetchStats = useCallback(async () => {
    if (!user) {
      setStats(null);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const { data, error: fetchError } = await getAccountStats();
    
    if (fetchError) {
      setError(fetchError);
    } else {
      setStats(data);
    }
    
    setLoading(false);
  }, [user]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    loading,
    error,
    refetch: fetchStats,
    clearError: () => setError(null)
  };
}

// Hook for tracking user activity
export function useUserActivity() {
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      // Update last seen on mount
      updateLastSeen();

      // Update last seen periodically
      const interval = setInterval(() => {
        updateLastSeen();
      }, 5 * 60 * 1000); // Every 5 minutes

      return () => clearInterval(interval);
    }
  }, [user]);

  // Update last seen on user interaction
  const trackActivity = useCallback(() => {
    if (user) {
      updateLastSeen();
    }
  }, [user]);

  return { trackActivity };
}

// Hook for form validation
export function useProfileValidation() {
  const validateProfile = useCallback((data: UpdateProfileRequest) => {
    const errors: Record<string, string> = {};

    if (data.full_name !== undefined) {
      if (data.full_name.length > 100) {
        errors.full_name = 'Full name must be less than 100 characters';
      }
    }

    if (data.company !== undefined) {
      if (data.company.length > 100) {
        errors.company = 'Company name must be less than 100 characters';
      }
    }

    if (data.job_title !== undefined) {
      if (data.job_title.length > 100) {
        errors.job_title = 'Job title must be less than 100 characters';
      }
    }

    if (data.phone !== undefined) {
      if (data.phone.length > 20) {
        errors.phone = 'Phone number must be less than 20 characters';
      }
      // Basic phone validation
      if (data.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(data.phone.replace(/[\s\-\(\)]/g, ''))) {
        errors.phone = 'Please enter a valid phone number';
      }
    }

    if (data.bio !== undefined) {
      if (data.bio.length > 500) {
        errors.bio = 'Bio must be less than 500 characters';
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }, []);

  const validatePassword = useCallback((data: ChangePasswordRequest) => {
    const errors: Record<string, string> = {};

    if (!data.current_password) {
      errors.current_password = 'Current password is required';
    }

    if (!data.new_password) {
      errors.new_password = 'New password is required';
    } else {
      if (data.new_password.length < 8) {
        errors.new_password = 'Password must be at least 8 characters long';
      }
      if (!/(?=.*[a-z])/.test(data.new_password)) {
        errors.new_password = 'Password must contain at least one lowercase letter';
      }
      if (!/(?=.*[A-Z])/.test(data.new_password)) {
        errors.new_password = 'Password must contain at least one uppercase letter';
      }
      if (!/(?=.*\d)/.test(data.new_password)) {
        errors.new_password = 'Password must contain at least one number';
      }
    }

    if (!data.confirm_password) {
      errors.confirm_password = 'Please confirm your new password';
    } else if (data.new_password !== data.confirm_password) {
      errors.confirm_password = 'Passwords do not match';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }, []);

  const validateEmail = useCallback((email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }, []);

  return {
    validateProfile,
    validatePassword,
    validateEmail
  };
}
