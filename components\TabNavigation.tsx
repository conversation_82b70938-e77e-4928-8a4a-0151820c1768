'use client';

import { useState } from 'react';
import { ChevronDown } from 'lucide-react';

interface TabNavigationProps {
  activeView: string;
  onViewChange: (view: string) => void;
}

const tabs = [
  { id: 'all_companies', label: 'All Companies' },
  { id: 'my_companies', label: 'All My Companies' },
  { id: 'portfolio', label: 'My Portfolio' },
];

const watchlists = [
  'Tech Stocks',
  'FMCG Watch',
  'New Energy',
];

export default function TabNavigation({ activeView, onViewChange }: TabNavigationProps) {
  const [selectedWatchlist, setSelectedWatchlist] = useState('Tech Stocks');
  const [watchlistOpen, setWatchlistOpen] = useState(false);

  const handleWatchlistSelect = (watchlist: string) => {
    setSelectedWatchlist(watchlist);
    setWatchlistOpen(false);
    onViewChange('watchlist');
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8 pt-4 border-b border-slate-200">
      <nav className="flex space-x-1 sm:space-x-2 overflow-x-auto">
        {/* Regular Tabs */}
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onViewChange(tab.id)}
            className={`px-2 sm:px-3 py-3 font-semibold text-xs sm:text-sm border-b-2 transition-colors whitespace-nowrap ${
              activeView === tab.id
                ? 'border-[#2c374b] text-[#2c374b]'
                : 'border-transparent text-slate-500 hover:text-slate-700'
            }`}
          >
            {tab.label}
          </button>
        ))}

        {/* Watchlist Dropdown Tab */}
        <div className="relative">
          <button
            onClick={() => {
              setWatchlistOpen(!watchlistOpen);
              onViewChange('watchlist');
            }}
            className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-3 font-semibold text-xs sm:text-sm border-b-2 transition-colors whitespace-nowrap ${
              activeView === 'watchlist'
                ? 'border-[#2c374b] text-[#2c374b]'
                : 'border-transparent text-slate-500 hover:text-slate-700'
            }`}
          >
            <span>{selectedWatchlist}</span>
            <ChevronDown 
              className={`text-xs w-3 h-3 transition-transform ${
                watchlistOpen ? 'rotate-180' : ''
              }`} 
            />
          </button>

          {/* Watchlist Dropdown */}
          {watchlistOpen && (
            <>
              {/* Backdrop */}
              <div 
                className="fixed inset-0 z-10" 
                onClick={() => setWatchlistOpen(false)}
              />
              
              {/* Dropdown Content */}
              <div className="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-xl z-20 py-1 border border-slate-200">
                {watchlists.map((watchlist) => (
                  <button
                    key={watchlist}
                    onClick={() => handleWatchlistSelect(watchlist)}
                    className="block w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-100"
                  >
                    {watchlist}
                  </button>
                ))}
              </div>
            </>
          )}
        </div>
      </nav>
    </div>
  );
}
