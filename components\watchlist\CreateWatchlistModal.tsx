'use client';

import { useState, useEffect, useRef } from 'react';
import { X, Plus } from 'lucide-react';
import { CreateWatchlistRequest } from '@/lib/watchlist/types';
import { validateWatchlistName } from '@/lib/watchlist/utils';
import LoadingSpinner from '../LoadingSpinner';

interface CreateWatchlistModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateWatchlist: (data: CreateWatchlistRequest) => Promise<{ success: boolean; error?: any }>;
  initialData?: { name: string; description?: string; is_default?: boolean } | null;
  isEditing?: boolean;
}

export default function CreateWatchlistModal({
  isOpen,
  onClose,
  onCreateWatchlist,
  initialData = null,
  isEditing = false
}: CreateWatchlistModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    is_default: false
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const nameInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen) {
      setFormData({
        name: initialData?.name || '',
        description: initialData?.description || '',
        is_default: initialData?.is_default || false
      });
      setErrors({});
      setIsSubmitting(false);
      
      // Focus name input when modal opens
      setTimeout(() => {
        nameInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen, initialData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const checked = type === 'checkbox' ? (e.target as HTMLInputElement).checked : undefined;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    // Validate name
    const nameError = validateWatchlistName(formData.name);
    if (nameError) {
      newErrors.name = nameError;
    }

    // Validate description length
    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await onCreateWatchlist({
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        is_default: formData.is_default
      });

      if (result.success) {
        onClose();
      } else {
        setErrors({
          general: result.error?.message || 'Failed to create watchlist'
        });
      }
    } catch {
      setErrors({
        general: 'An unexpected error occurred'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[2000] overflow-y-auto">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/40 transition-opacity z-[2000]"
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Modal */}
      <div className="relative z-[2100] flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-lg shadow-xl w-full max-w-md">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-slate-200">
            <h2 className="text-lg font-semibold text-slate-900">
              {isEditing ? 'Edit Watchlist' : 'Create New Watchlist'}
            </h2>
            <button
              type="button"
              onClick={onClose}
              className="text-slate-400 hover:text-slate-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Content */}
          <form
            onSubmit={handleSubmit}
            className="p-6 space-y-6"
            onClick={(e) => e.stopPropagation()}
            onMouseDown={(e) => e.stopPropagation()}
            onMouseUp={(e) => e.stopPropagation()}
          >
            {/* General Error */}
            {errors.general && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{errors.general}</p>
              </div>
            )}

            {/* Watchlist Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-slate-700 mb-2">
                Watchlist Name <span className="text-red-500">*</span>
              </label>
              <input
                ref={nameInputRef}
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter watchlist name"
                className={`
                  w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--strikedeck-blue)]
                  ${errors.name ? 'border-red-300 focus:border-red-500' : 'border-slate-300 focus:border-[var(--strikedeck-blue)]'}
                `}
                autoFocus
                disabled={isSubmitting}
                required
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-slate-700 mb-2">
                Description (Optional)
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Add a description for your watchlist"
                rows={3}
                className={`
                  w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--strikedeck-blue)] resize-none
                  ${errors.description ? 'border-red-300 focus:border-red-500' : 'border-slate-300 focus:border-[var(--strikedeck-blue)]'}
                `}
                disabled={isSubmitting}
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description}</p>
              )}
              <p className="mt-1 text-xs text-slate-500">
                {formData.description.length}/500 characters
              </p>
            </div>

            {/* Set as Default */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_default"
                name="is_default"
                checked={formData.is_default}
                onChange={handleInputChange}
                className="h-4 w-4 text-[var(--strikedeck-blue)] focus:ring-[var(--strikedeck-blue)] border-slate-300 rounded"
                disabled={isSubmitting}
              />
              <label htmlFor="is_default" className="ml-2 block text-sm text-slate-700">
                Set as default watchlist
              </label>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[var(--strikedeck-blue)] disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-white bg-[var(--strikedeck-blue)] border border-transparent rounded-lg hover:bg-[var(--strikedeck-blue-dark)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[var(--strikedeck-blue)] disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {isSubmitting ? (
                  <>
                    <LoadingSpinner size="sm" />
                    <span>{isEditing ? 'Updating...' : 'Creating...'}</span>
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4" />
                    <span>{isEditing ? 'Update Watchlist' : 'Create Watchlist'}</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
