import React from 'react';

interface TestDataAccessProps {
  data: any;
}

export default function TestDataAccess({ data }: TestDataAccessProps) {
  // Test the data access patterns we implemented
  const qrData = data.extracted_data?.quarterly_results || 
                data.summary?.extracted_data?.quarterly_results || 
                {};

  const companyInfo = data.company_information || 
                     data.summary?.company_information;

  const categories = data.categories || data.summary?.categories;
  const primaryCategory = data.primary_category || data.summary?.primary_category;
  const implications = data.investment_implications || data.summary?.investment_implications;
  const summaryText = data.summary || data.summary?.summary;

  console.log('Data Access Test Results:', {
    hasQuarterlyResults: !!qrData.revenue,
    quarterlyResultsData: qrData,
    companyInfo,
    categories,
    primaryCategory,
    implications,
    summaryText: typeof summaryText === 'string' ? summaryText : summaryText?.summary
  });

  return (
    <div className="p-4 bg-gray-50 rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Data Access Test Results</h3>
      
      {/* Financial Data Test */}
      <div className="mb-4">
        <h4 className="font-medium text-blue-700">Financial Data Access:</h4>
        <div className="text-sm space-y-1">
          <p>Has Quarterly Results: {qrData.revenue ? '✅ YES' : '❌ NO'}</p>
          {qrData.revenue && (
            <>
              <p>Revenue: {qrData.revenue.current_quarter} ({qrData.revenue.yoy_growth_percentage} YoY)</p>
              <p>EBITDA: {qrData.ebitda?.current_quarter} ({qrData.ebitda?.yoy_growth_percentage} YoY)</p>
              <p>PAT: {qrData.pat?.current_quarter} ({qrData.pat?.yoy_growth_percentage} YoY)</p>
            </>
          )}
        </div>
      </div>

      {/* Company Info Test */}
      <div className="mb-4">
        <h4 className="font-medium text-green-700">Company Information:</h4>
        <div className="text-sm space-y-1">
          <p>Company: {companyInfo?.company_name || 'Not found'}</p>
          <p>Quarter: {companyInfo?.quarter || 'Not found'}</p>
          <p>Filing Date: {companyInfo?.filing_date || 'Not found'}</p>
        </div>
      </div>

      {/* Classification Test */}
      <div className="mb-4">
        <h4 className="font-medium text-purple-700">Classification:</h4>
        <div className="text-sm space-y-1">
          <p>Categories: {categories ? categories.join(', ') : 'Not found'}</p>
          <p>Primary: {primaryCategory || 'Not found'}</p>
        </div>
      </div>

      {/* Summary Test */}
      <div className="mb-4">
        <h4 className="font-medium text-orange-700">Summary:</h4>
        <div className="text-sm">
          <p>{typeof summaryText === 'string' ? summaryText : summaryText?.summary || 'Not found'}</p>
        </div>
      </div>

      {/* Investment Implications Test */}
      <div className="mb-4">
        <h4 className="font-medium text-red-700">Investment Implications:</h4>
        <div className="text-sm">
          {implications && implications.length > 0 ? (
            <ul className="list-disc list-inside space-y-1">
              {implications.map((implication: string, idx: number) => (
                <li key={idx}>{implication}</li>
              ))}
            </ul>
          ) : (
            <p>Not found</p>
          )}
        </div>
      </div>
    </div>
  );
}
