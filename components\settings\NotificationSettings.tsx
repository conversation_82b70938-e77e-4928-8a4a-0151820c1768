'use client';

import { useState, useEffect } from 'react';
import { Bell, Mail, Smartphone, AlertCircle } from 'lucide-react';
import { NotificationPreferences } from '@/lib/profile/types';
import LoadingSpinner from '../LoadingSpinner';

interface NotificationSettingsProps {
  preferences: NotificationPreferences;
  onUpdate: (updates: Partial<NotificationPreferences>) => Promise<{ success: boolean; error?: any }>;
  loading?: boolean;
}

export default function NotificationSettings({
  preferences,
  onUpdate,
  loading = false
}: NotificationSettingsProps) {
  const [localPreferences, setLocalPreferences] = useState<NotificationPreferences>(preferences);
  const [isUpdating, setIsUpdating] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    setLocalPreferences(preferences);
    setHasChanges(false);
  }, [preferences]);

  const handleToggle = (key: keyof NotificationPreferences) => {
    const newPreferences = {
      ...localPreferences,
      [key]: !localPreferences[key]
    };
    
    setLocalPreferences(newPreferences);
    
    // Check if there are changes
    const hasChanges = Object.keys(newPreferences).some(
      k => newPreferences[k as keyof NotificationPreferences] !== preferences[k as keyof NotificationPreferences]
    );
    setHasChanges(hasChanges);
  };

  const handleSave = async () => {
    setIsUpdating(true);
    
    try {
      const result = await onUpdate(localPreferences);
      if (result.success) {
        setHasChanges(false);
      }
    } catch (error) {
      console.error('Failed to update notification preferences:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleReset = () => {
    setLocalPreferences(preferences);
    setHasChanges(false);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Email Notifications */}
      <div className="bg-white border border-slate-200 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Mail className="w-5 h-5 text-slate-600" />
          <div>
            <h3 className="text-lg font-medium text-slate-900">Email Notifications</h3>
            <p className="text-sm text-slate-600">Choose what email notifications you&apos;d like to receive</p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-slate-900">Email Alerts</h4>
              <p className="text-sm text-slate-600">General email notifications and updates</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={localPreferences.email_alerts}
                onChange={() => handleToggle('email_alerts')}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-slate-900">Watchlist Alerts</h4>
              <p className="text-sm text-slate-600">Notifications about your watchlist companies</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={localPreferences.watchlist_alerts}
                onChange={() => handleToggle('watchlist_alerts')}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-slate-900">Filing Alerts</h4>
              <p className="text-sm text-slate-600">New corporate filings from tracked companies</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={localPreferences.filing_alerts}
                onChange={() => handleToggle('filing_alerts')}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-slate-900">Marketing Emails</h4>
              <p className="text-sm text-slate-600">Product updates, tips, and promotional content</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={localPreferences.marketing_emails}
                onChange={() => handleToggle('marketing_emails')}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Push Notifications */}
      <div className="bg-white border border-slate-200 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Bell className="w-5 h-5 text-slate-600" />
          <div>
            <h3 className="text-lg font-medium text-slate-900">Push Notifications</h3>
            <p className="text-sm text-slate-600">Browser and mobile push notifications</p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-slate-900">Push Notifications</h4>
              <p className="text-sm text-slate-600">Real-time notifications in your browser</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={localPreferences.push_notifications || false}
                onChange={() => handleToggle('push_notifications' as keyof NotificationPreferences)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-blue-800">Browser Permission Required</h4>
                <p className="text-sm text-blue-700 mt-1">
                  To receive push notifications, you&apos;ll need to allow notifications in your browser when prompted.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* SMS Notifications */}
      <div className="bg-white border border-slate-200 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Smartphone className="w-5 h-5 text-slate-600" />
          <div>
            <h3 className="text-lg font-medium text-slate-900">SMS Notifications</h3>
            <p className="text-sm text-slate-600">Text message notifications for urgent alerts</p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-slate-900">SMS Alerts</h4>
              <p className="text-sm text-slate-600">Critical alerts via text message</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={localPreferences.sms_alerts || false}
                onChange={() => handleToggle('sms_alerts' as keyof NotificationPreferences)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">Phone Number Required</h4>
                <p className="text-sm text-yellow-700 mt-1">
                  Add a phone number to your profile to receive SMS notifications.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Save Changes */}
      {hasChanges && (
        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-slate-200">
          <button
            onClick={handleReset}
            disabled={isUpdating}
            className="px-4 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            Reset
          </button>
          
          <button
            onClick={handleSave}
            disabled={isUpdating}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isUpdating && <LoadingSpinner size="sm" />}
            <span>Save Changes</span>
          </button>
        </div>
      )}
    </div>
  );
}
