import { supabase } from '@/lib/supabase';

export interface CreateFeedbackInput {
  filing_id: string;
  text: string;
  status?: 'pending' | 'reviewed' | 'resolved';
  user_id?: string | null;
}

export interface FeedbackRow {
  id: string;
  filing_id: string;
  text: string;
  status: 'pending' | 'reviewed' | 'resolved';
  user_id: string | null;
  created_at: string;
  updated_at: string;
}

export async function createFeedback({ filing_id, text, status = 'pending', user_id }: CreateFeedbackInput): Promise<FeedbackRow> {
  const { data, error } = await supabase
    .from('feedback')
    .insert([{ filing_id, text, status, user_id: user_id ?? null }])
    .select()
    .single();

  if (error) throw new Error(error.message);
  return data as FeedbackRow;
}

export async function getFeedbackCount(filing_id: string): Promise<number> {
  const { count, error } = await supabase
    .from('feedback')
    .select('*', { count: 'exact', head: true })
    .eq('filing_id', filing_id);

  if (error) throw new Error(error.message);
  return count || 0;
}

export async function getFeedbackList(filing_id: string, limit: number = 20): Promise<FeedbackRow[]> {
  const { data, error } = await supabase
    .from('feedback')
    .select('*')
    .eq('filing_id', filing_id)
    .order('created_at', { ascending: false })
    .limit(limit);

  if (error) throw new Error(error.message);
  return (data || []) as FeedbackRow[];
}

