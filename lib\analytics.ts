import React from 'react';
import { onCLS, onINP, onFCP, onLCP, onTTFB, type Metric } from 'web-vitals';
import { trackPerformanceWithAlerts } from './performance-alerts';

// Performance thresholds (in milliseconds)
const PERFORMANCE_THRESHOLDS = {
  FCP: 1800, // First Contentful Paint
  LCP: 2500, // Largest Contentful Paint
  INP: 200,  // Interaction to Next Paint (replaces FID)
  CLS: 0.1,  // Cumulative Layout Shift
  TTFB: 800, // Time to First Byte
} as const;

// Performance monitoring function with alerting
function sendToAnalytics(metric: Metric) {
  // Track with alerts
  trackPerformanceWithAlerts(metric.name, metric.value, window.location.pathname);

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    const threshold = PERFORMANCE_THRESHOLDS[metric.name as keyof typeof PERFORMANCE_THRESHOLDS];
    const status = metric.value <= threshold ? '✅ GOOD' : '⚠️ NEEDS IMPROVEMENT';

    console.log(`[Performance] ${metric.name}: ${metric.value}ms ${status}`);
  }

  // In production, you would send this to your analytics service
  // Example: analytics.track('web-vital', { metric: metric.name, value: metric.value });
}

// Database query performance monitoring
export function monitorQueryPerformance<T>(
  queryName: string,
  queryFn: () => Promise<T>
): Promise<T> {
  const startTime = performance.now();
  
  return queryFn()
    .then((result) => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Log slow queries
      if (duration > 200) {
        console.warn(`[Query Performance] Slow query detected: ${queryName} took ${duration.toFixed(2)}ms`);
      } else if (process.env.NODE_ENV === 'development') {
        console.log(`[Query Performance] ${queryName}: ${duration.toFixed(2)}ms`);
      }
      
      return result;
    })
    .catch((error) => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      console.error(`[Query Performance] Query failed: ${queryName} after ${duration.toFixed(2)}ms`, error);
      throw error;
    });
}

// Initialize Web Vitals monitoring
export function initPerformanceMonitoring() {
  // Only run in browser
  if (typeof window === 'undefined') return;

  // Monitor Core Web Vitals
  onCLS(sendToAnalytics);
  onINP(sendToAnalytics);
  onFCP(sendToAnalytics);
  onLCP(sendToAnalytics);
  onTTFB(sendToAnalytics);

  // Monitor custom performance metrics
  if ('performance' in window && 'getEntriesByType' in performance) {
    // Monitor navigation timing
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
        const loadComplete = navigation.loadEventEnd - navigation.loadEventStart;
        
        console.log(`[Performance] DOM Content Loaded: ${domContentLoaded.toFixed(2)}ms`);
        console.log(`[Performance] Load Complete: ${loadComplete.toFixed(2)}ms`);
      }
    });
  }
}

// Component render performance monitoring
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
) {
  return function PerformanceMonitoredComponent(props: P) {
    const startTime = performance.now();
    
    React.useEffect(() => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      if (renderTime > 16) { // More than one frame at 60fps
        console.warn(`[Component Performance] Slow render: ${componentName} took ${renderTime.toFixed(2)}ms`);
      }
    });
    
    return React.createElement(Component, props);
  };
}

// Memory usage monitoring
export function monitorMemoryUsage() {
  if (typeof window === 'undefined' || !('performance' in window)) return;
  
  const memory = (performance as any).memory;
  if (memory) {
    const used = memory.usedJSHeapSize / 1024 / 1024;
    const total = memory.totalJSHeapSize / 1024 / 1024;
    const limit = memory.jsHeapSizeLimit / 1024 / 1024;
    
    console.log(`[Memory] Used: ${used.toFixed(2)}MB, Total: ${total.toFixed(2)}MB, Limit: ${limit.toFixed(2)}MB`);
    
    // Warn if memory usage is high
    if (used / limit > 0.8) {
      console.warn('[Memory] High memory usage detected');
    }
  }
}
