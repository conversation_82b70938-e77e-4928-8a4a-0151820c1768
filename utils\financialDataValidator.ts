/**
 * Strict Type Validation for Financial Data
 * 
 * This utility ensures that financial data is properly typed and validated
 * before being sent to the frontend to prevent any display errors.
 */

export interface ValidatedFinancialMetric {
  current: number;
  previous: number;
  growth: number;
}

export interface ValidatedFinancialMetrics {
  revenue?: ValidatedFinancialMetric;
  ebitda?: ValidatedFinancialMetric;
  pat?: ValidatedFinancialMetric;
  other_metrics?: {
    earnings_per_share?: number;
  };
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  data?: ValidatedFinancialMetrics;
}

/**
 * Validates and converts a single financial metric to ensure type safety
 */
export function validateFinancialMetric(
  metric: any,
  metricName: string
): { isValid: boolean; errors: string[]; data?: ValidatedFinancialMetric } {
  const errors: string[] = [];
  
  if (!metric || typeof metric !== 'object') {
    errors.push(`${metricName}: Must be an object`);
    return { isValid: false, errors };
  }

  // Validate and convert current value
  let current: number;
  if (metric.current === null || metric.current === undefined) {
    errors.push(`${metricName}.current: Cannot be null or undefined`);
  } else if (typeof metric.current === 'number') {
    if (isNaN(metric.current)) {
      errors.push(`${metricName}.current: Cannot be NaN`);
    } else {
      current = metric.current;
    }
  } else if (typeof metric.current === 'string') {
    const parsed = parseFloat(metric.current);
    if (isNaN(parsed)) {
      errors.push(`${metricName}.current: String "${metric.current}" cannot be converted to number`);
    } else {
      current = parsed;
    }
  } else {
    errors.push(`${metricName}.current: Must be a number or numeric string, got ${typeof metric.current}`);
  }

  // Validate and convert previous value
  let previous: number;
  if (metric.previous === null || metric.previous === undefined) {
    errors.push(`${metricName}.previous: Cannot be null or undefined`);
  } else if (typeof metric.previous === 'number') {
    if (isNaN(metric.previous)) {
      errors.push(`${metricName}.previous: Cannot be NaN`);
    } else {
      previous = metric.previous;
    }
  } else if (typeof metric.previous === 'string') {
    const parsed = parseFloat(metric.previous);
    if (isNaN(parsed)) {
      errors.push(`${metricName}.previous: String "${metric.previous}" cannot be converted to number`);
    } else {
      previous = parsed;
    }
  } else {
    errors.push(`${metricName}.previous: Must be a number or numeric string, got ${typeof metric.previous}`);
  }

  // Validate and convert growth value
  let growth: number;
  if (metric.growth === null || metric.growth === undefined) {
    errors.push(`${metricName}.growth: Cannot be null or undefined`);
  } else if (typeof metric.growth === 'number') {
    if (isNaN(metric.growth)) {
      errors.push(`${metricName}.growth: Cannot be NaN`);
    } else {
      growth = metric.growth;
    }
  } else if (typeof metric.growth === 'string') {
    // Handle string growth values like "+11%" or "11"
    const cleanGrowth = metric.growth.replace(/[^\d.-]/g, '');
    const parsed = parseFloat(cleanGrowth);
    if (isNaN(parsed)) {
      errors.push(`${metricName}.growth: String "${metric.growth}" cannot be converted to number`);
    } else {
      growth = parsed;
    }
  } else {
    errors.push(`${metricName}.growth: Must be a number or numeric string, got ${typeof metric.growth}`);
  }

  if (errors.length > 0) {
    return { isValid: false, errors };
  }

  return {
    isValid: true,
    errors: [],
    data: { current: current!, previous: previous!, growth: growth! }
  };
}

/**
 * Validates the entire financial metrics object
 */
export function validateFinancialMetrics(data: any): ValidationResult {
  const errors: string[] = [];
  const validatedData: ValidatedFinancialMetrics = {};

  if (!data || typeof data !== 'object') {
    return {
      isValid: false,
      errors: ['Financial metrics must be an object'],
    };
  }

  // Validate revenue
  if (data.revenue) {
    const result = validateFinancialMetric(data.revenue, 'revenue');
    if (!result.isValid) {
      errors.push(...result.errors);
    } else {
      validatedData.revenue = result.data;
    }
  }

  // Validate ebitda
  if (data.ebitda) {
    const result = validateFinancialMetric(data.ebitda, 'ebitda');
    if (!result.isValid) {
      errors.push(...result.errors);
    } else {
      validatedData.ebitda = result.data;
    }
  }

  // Validate pat
  if (data.pat) {
    const result = validateFinancialMetric(data.pat, 'pat');
    if (!result.isValid) {
      errors.push(...result.errors);
    } else {
      validatedData.pat = result.data;
    }
  }

  // Validate other_metrics
  if (data.other_metrics) {
    if (typeof data.other_metrics !== 'object') {
      errors.push('other_metrics: Must be an object');
    } else {
      validatedData.other_metrics = {};
      
      if (data.other_metrics.earnings_per_share !== undefined) {
        if (typeof data.other_metrics.earnings_per_share === 'number') {
          if (isNaN(data.other_metrics.earnings_per_share)) {
            errors.push('other_metrics.earnings_per_share: Cannot be NaN');
          } else {
            validatedData.other_metrics.earnings_per_share = data.other_metrics.earnings_per_share;
          }
        } else if (typeof data.other_metrics.earnings_per_share === 'string') {
          const parsed = parseFloat(data.other_metrics.earnings_per_share);
          if (isNaN(parsed)) {
            errors.push(`other_metrics.earnings_per_share: String "${data.other_metrics.earnings_per_share}" cannot be converted to number`);
          } else {
            validatedData.other_metrics.earnings_per_share = parsed;
          }
        } else {
          errors.push(`other_metrics.earnings_per_share: Must be a number or numeric string, got ${typeof data.other_metrics.earnings_per_share}`);
        }
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    data: errors.length === 0 ? validatedData : undefined,
  };
}

/**
 * Sanitizes and validates financial data before sending to frontend
 * This should be called in your backend/API layer
 */
export function sanitizeFinancialData(rawData: any): ValidatedFinancialMetrics | null {
  const validation = validateFinancialMetrics(rawData);
  
  if (!validation.isValid) {
    console.error('Financial data validation failed:', validation.errors);
    // In production, you might want to log this to your error tracking service
    return null;
  }
  
  return validation.data!;
}

/**
 * Type guard to check if data is valid financial metrics
 */
export function isValidFinancialMetrics(data: any): data is ValidatedFinancialMetrics {
  const validation = validateFinancialMetrics(data);
  return validation.isValid;
}

/**
 * Example usage in your backend/API:
 * 
 * const rawFinancialData = {
 *   revenue: { current: "543.7", previous: 489.7, growth: "11%" },
 *   ebitda: { current: 67.9, previous: "64.9", growth: 4.6 },
 *   pat: { current: 33.6, previous: 34.9, growth: -3.7 }
 * };
 * 
 * const validatedData = sanitizeFinancialData(rawFinancialData);
 * if (validatedData) {
 *   // Send to frontend
 *   response.json({ financial_metrics: validatedData });
 * } else {
 *   // Handle validation error
 *   response.status(400).json({ error: "Invalid financial data" });
 * }
 */
