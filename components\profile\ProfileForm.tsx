'use client';

import { useState, useEffect } from 'react';
import { Save, X } from 'lucide-react';
import { ProfileFormProps, ProfileFormData, ProfileFormErrors } from '@/lib/profile/types';
import { useProfileValidation } from '@/lib/profile/hooks';
import LoadingSpinner from '../LoadingSpinner';

export default function ProfileForm({
  profile,
  onUpdate,
  loading = false
}: ProfileFormProps) {
  const [formData, setFormData] = useState<ProfileFormData>({
    full_name: '',
    company: '',
    job_title: '',
    phone: '',
    bio: ''
  });
  const [errors, setErrors] = useState<ProfileFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const { validateProfile } = useProfileValidation();

  // Initialize form data from profile
  useEffect(() => {
    if (profile) {
      const initialData = {
        full_name: profile.full_name || '',
        company: profile.company || '',
        job_title: profile.job_title || '',
        phone: profile.phone || '',
        bio: profile.bio || ''
      };
      setFormData(initialData);
      setHasChanges(false);
    }
  }, [profile]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    setFormData(prev => {
      const newData = { ...prev, [name]: value };
      
      // Check if there are changes
      const originalData = {
        full_name: profile?.full_name || '',
        company: profile?.company || '',
        job_title: profile?.job_title || '',
        phone: profile?.phone || '',
        bio: profile?.bio || ''
      };
      
      const hasChanges = Object.keys(newData).some(
        key => newData[key as keyof ProfileFormData] !== originalData[key as keyof ProfileFormData]
      );
      
      setHasChanges(hasChanges);
      
      return newData;
    });

    // Clear error when user starts typing
    if (errors[name as keyof ProfileFormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    const validation = validateProfile(formData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const result = await onUpdate(formData);
      
      if (result.success) {
        setHasChanges(false);
      } else {
        setErrors({
          general: result.error?.message || 'Failed to update profile'
        });
      }
    } catch {
      setErrors({
        general: 'An unexpected error occurred'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    if (profile) {
      setFormData({
        full_name: profile.full_name || '',
        company: profile.company || '',
        job_title: profile.job_title || '',
        phone: profile.phone || '',
        bio: profile.bio || ''
      });
      setHasChanges(false);
      setErrors({});
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* General Error */}
      {errors.general && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">{errors.general}</p>
        </div>
      )}

      {/* Full Name */}
      <div>
        <label htmlFor="full_name" className="block text-sm font-medium text-slate-700 mb-2">
          Full Name
        </label>
        <input
          type="text"
          id="full_name"
          name="full_name"
          value={formData.full_name}
          onChange={handleInputChange}
          placeholder="Enter your full name"
          className={`
            w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500
            ${errors.full_name ? 'border-red-300 focus:border-red-500' : 'border-slate-300 focus:border-blue-500'}
          `}
          disabled={isSubmitting}
        />
        {errors.full_name && (
          <p className="mt-1 text-sm text-red-600">{errors.full_name}</p>
        )}
      </div>

      {/* Company */}
      <div>
        <label htmlFor="company" className="block text-sm font-medium text-slate-700 mb-2">
          Company
        </label>
        <input
          type="text"
          id="company"
          name="company"
          value={formData.company}
          onChange={handleInputChange}
          placeholder="Enter your company name"
          className={`
            w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500
            ${errors.company ? 'border-red-300 focus:border-red-500' : 'border-slate-300 focus:border-blue-500'}
          `}
          disabled={isSubmitting}
        />
        {errors.company && (
          <p className="mt-1 text-sm text-red-600">{errors.company}</p>
        )}
      </div>

      {/* Job Title */}
      <div>
        <label htmlFor="job_title" className="block text-sm font-medium text-slate-700 mb-2">
          Job Title
        </label>
        <input
          type="text"
          id="job_title"
          name="job_title"
          value={formData.job_title}
          onChange={handleInputChange}
          placeholder="Enter your job title"
          className={`
            w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500
            ${errors.job_title ? 'border-red-300 focus:border-red-500' : 'border-slate-300 focus:border-blue-500'}
          `}
          disabled={isSubmitting}
        />
        {errors.job_title && (
          <p className="mt-1 text-sm text-red-600">{errors.job_title}</p>
        )}
      </div>

      {/* Phone */}
      <div>
        <label htmlFor="phone" className="block text-sm font-medium text-slate-700 mb-2">
          Phone Number
        </label>
        <input
          type="tel"
          id="phone"
          name="phone"
          value={formData.phone}
          onChange={handleInputChange}
          placeholder="Enter your phone number"
          className={`
            w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500
            ${errors.phone ? 'border-red-300 focus:border-red-500' : 'border-slate-300 focus:border-blue-500'}
          `}
          disabled={isSubmitting}
        />
        {errors.phone && (
          <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
        )}
      </div>

      {/* Bio */}
      <div>
        <label htmlFor="bio" className="block text-sm font-medium text-slate-700 mb-2">
          Bio
        </label>
        <textarea
          id="bio"
          name="bio"
          value={formData.bio}
          onChange={handleInputChange}
          placeholder="Tell us about yourself..."
          rows={4}
          className={`
            w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none
            ${errors.bio ? 'border-red-300 focus:border-red-500' : 'border-slate-300 focus:border-blue-500'}
          `}
          disabled={isSubmitting}
        />
        {errors.bio && (
          <p className="mt-1 text-sm text-red-600">{errors.bio}</p>
        )}
        <p className="mt-1 text-xs text-slate-500">
          {formData.bio.length}/500 characters
        </p>
      </div>

      {/* Action Buttons */}
      {hasChanges && (
        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-slate-200">
          <button
            type="button"
            onClick={handleReset}
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 flex items-center space-x-2"
          >
            <X className="w-4 h-4" />
            <span>Cancel</span>
          </button>
          
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isSubmitting ? (
              <>
                <LoadingSpinner size="sm" />
                <span>Saving...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>Save Changes</span>
              </>
            )}
          </button>
        </div>
      )}
    </form>
  );
}
