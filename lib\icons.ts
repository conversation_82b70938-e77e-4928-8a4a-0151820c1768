// Centralized icon imports for tree-shaking optimization
// Only import the icons we actually use to reduce bundle size

export {
  // Navigation and UI
  Menu,
  X,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  // Extra UI icons used in app
  UserCircle,
  LogOut,
  Newspaper,
  Star,
  Briefcase,
  HelpCircle,
  Target,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Building2,
  Copy,
  Phone,
  Building,
  Shield,
  Bell,
  List,

  // Actions
  RefreshCw,
  Download,
  Settings,
  Plus,
  Trash2,
  Edit,
  Save,

  // Status and Feedback
  AlertCircle,
  AlertTriangle,
  CheckCircle,
  Info,
  Flag,

  // Content
  FileText,
  ExternalLink,
  Calendar,
  Clock,
  Tag,
  Search,
  Filter,
  MessageSquare,
  Minus,
  Play,

  // Data and Analytics
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,

  // User and Profile
  User,
  Users,
  Eye,
  EyeOff,

  // Drag and Drop (only for watchlist)
  GripVertical,
  Move,

  // Loading
  Loader2,
} from 'lucide-react';

// Re-export commonly used icons with descriptive names
export { RefreshCw as LoadingIcon } from 'lucide-react';
export { AlertCircle as ErrorIcon } from 'lucide-react';
export { CheckCircle as SuccessIcon } from 'lucide-react';

// Extra icons re-exported individually to keep main export clean
export { Check } from './icons-extras';

export { TrendingUp as PositiveIcon } from 'lucide-react';
export { TrendingDown as NegativeIcon } from 'lucide-react';
