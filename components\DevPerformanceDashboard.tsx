"use client";

// Dev-only wrapper to conditionally render PerformanceDashboard on the client
import { useEffect, useState } from 'react';
import PerformanceDashboard from './PerformanceDashboard';

export default function DevPerformanceDashboard() {
  const [show, setShow] = useState(false);
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      setShow(true);
    }
  }, []);
  if (!show) return null;
  return <PerformanceDashboard />;
}

