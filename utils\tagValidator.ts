/**
 * Tag Validation Utility
 * 
 * Ensures that all tags are strictly validated against predefined categories.
 * If a tag doesn't match any predefined category, it's marked as "Others".
 */

// Predefined tag categories - must match document_analysis_config.yaml
export const PREDEFINED_TAGS = {
  financial_performance: [
    "Quarterly Results",
    "Annual Results", 
    "Revenue Growth",
    "Profit Growth",
    "Loss Reported",
    "Margin Expansion",
    "Margin Compression",
    "EBITDA Growth",
    "PAT Growth",
    "Financial Performance",
    "Strong Results",
    "Weak Results"
  ],
  
  operational_metrics: [
    "Operational Efficiency",
    "Cost Optimization",
    "Volume Growth",
    "Market Share",
    "Capacity Utilization",
    "Production Increase",
    "Operational Excellence",
    "Process Improvement"
  ],
  
  business_expansion: [
    "Business Expansion",
    "New Markets",
    "Geographic Expansion",
    "Product Launch",
    "Service Launch",
    "Market Entry",
    "Diversification",
    "Growth Strategy"
  ],
  
  contracts_orders: [
    "Contract Award",
    "Order Win",
    "New Contract",
    "Order Book",
    "Contract Renewal",
    "Major Order",
    "Government Contract",
    "Private Contract"
  ],
  
  corporate_actions: [
    "Dividend",
    "Bonus Issue",
    "Stock Split",
    "Rights Issue",
    "Share Buyback",
    "Delisting",
    "Listing",
    "Corporate Action"
  ],
  
  mergers_acquisitions: [
    "Merger",
    "Acquisition",
    "Joint Venture",
    "Strategic Partnership",
    "Divestiture",
    "Spin-off",
    "M&A",
    "Business Combination"
  ],
  
  regulatory_compliance: [
    "Regulatory Filing",
    "Compliance",
    "SEBI Disclosure",
    "Statutory Filing",
    "Regulatory Update",
    "Legal Compliance",
    "Governance",
    "Regulatory Approval"
  ],
  
  insider_activities: [
    "Insider Trading",
    "Promoter Transaction",
    "Director Transaction",
    "Share Purchase",
    "Share Sale",
    "Insider Disclosure",
    "Promoter Pledge",
    "Shareholding Change"
  ],
  
  earnings_calls: [
    "Earnings Call",
    "Conference Call",
    "Investor Call",
    "Analyst Meet",
    "Investor Presentation",
    "Management Discussion",
    "Q&A Session",
    "Investor Update",
    "Earnings Call Transcript",
    "Call Recording",
    "Audio Transcript",
    "Video Transcript",
    "Investor Meeting",
    "Individual Investors",
    "Institutional Investors",
    "Analyst Meeting"
  ],
  
  strategic_initiatives: [
    "Strategic Initiative",
    "Digital Transformation",
    "Technology Upgrade",
    "Innovation",
    "R&D Investment",
    "Sustainability",
    "ESG Initiative",
    "Strategic Partnership"
  ],
  
  market_conditions: [
    "Market Outlook",
    "Industry Trends",
    "Economic Impact",
    "Market Challenges",
    "Competitive Landscape",
    "Sector Performance",
    "Market Dynamics",
    "External Factors"
  ],
  
  others: [
    "Corporate Update",
    "General Announcement",
    "Business Update",
    "Company News",
    "Press Release",
    "Others",
    "Miscellaneous",
    "General"
  ],

  press_release: [
    "Press Release",
    "Media Release",
    "News Release",
    "Corporate Announcement",
    "Public Statement",
    "Company Statement",
    "Official Release",
    "Media Statement"
  ]
};

// Create a flat list of all valid tags
export const ALL_VALID_TAGS = Object.values(PREDEFINED_TAGS).flat();

/**
 * Validates a single tag against predefined categories
 */
export function validateTag(tag: string): string {
  if (!tag || typeof tag !== 'string') {
    return 'Others';
  }
  
  const trimmedTag = tag.trim();
  
  // Check for exact match (case-sensitive)
  if (ALL_VALID_TAGS.includes(trimmedTag)) {
    return trimmedTag;
  }
  
  // Check for case-insensitive match
  const lowerTag = trimmedTag.toLowerCase();
  const matchedTag = ALL_VALID_TAGS.find(validTag => 
    validTag.toLowerCase() === lowerTag
  );
  
  if (matchedTag) {
    return matchedTag;
  }
  
  // Check for partial matches with high confidence
  const partialMatch = findBestPartialMatch(trimmedTag);
  if (partialMatch) {
    return partialMatch;
  }
  
  // If no match found, return 'Others'
  return 'Others';
}

/**
 * Finds the best partial match for a tag
 */
function findBestPartialMatch(inputTag: string): string | null {
  const lowerInput = inputTag.toLowerCase();
  
  // Define high-confidence partial matches
  const partialMatches: { [key: string]: string } = {
    // Financial terms
    'quarterly': 'Quarterly Results',
    'annual': 'Annual Results',
    'q1': 'Quarterly Results',
    'q2': 'Quarterly Results', 
    'q3': 'Quarterly Results',
    'q4': 'Quarterly Results',
    'results': 'Quarterly Results',
    'revenue': 'Revenue Growth',
    'profit': 'Profit Growth',
    'loss': 'Loss Reported',
    'margin': 'Margin Expansion',
    'ebitda': 'EBITDA Growth',
    'pat': 'PAT Growth',
    
    // Operational terms
    'efficiency': 'Operational Efficiency',
    'optimization': 'Cost Optimization',
    'operational': 'Operational Efficiency',
    
    // Business terms
    'expansion': 'Business Expansion',
    'launch': 'Product Launch',
    'growth': 'Revenue Growth',
    
    // Contract terms
    'contract': 'Contract Award',
    'order': 'Order Win',
    'award': 'Contract Award',
    
    // Corporate actions
    'dividend': 'Dividend',
    'bonus': 'Bonus Issue',
    'split': 'Stock Split',
    'buyback': 'Share Buyback',
    
    // M&A terms
    'merger': 'Merger',
    'acquisition': 'Acquisition',
    'joint venture': 'Joint Venture',
    
    // Regulatory terms
    'regulatory': 'Regulatory Filing',
    'compliance': 'Compliance',
    'sebi': 'SEBI Disclosure',
    
    // Insider terms
    'insider': 'Insider Trading',
    'promoter': 'Promoter Transaction',
    
    // Call terms
    'call': 'Earnings Call',
    'conference': 'Conference Call',
    'investor': 'Investor Call'
  };
  
  // Check for partial matches
  for (const [key, value] of Object.entries(partialMatches)) {
    if (lowerInput.includes(key)) {
      return value;
    }
  }
  
  return null;
}

/**
 * Validates an array of tags and returns validated tags
 */
export function validateTags(tags: string[]): string[] {
  if (!Array.isArray(tags)) {
    return ['Others'];
  }
  
  const validatedTags = tags
    .slice(0, 3) // Limit to max 3 tags
    .map(tag => validateTag(tag))
    .filter((tag, index, array) => array.indexOf(tag) === index); // Remove duplicates
  
  // Ensure at least one tag
  if (validatedTags.length === 0) {
    return ['Others'];
  }
  
  return validatedTags;
}

/**
 * Gets the category of a validated tag
 */
export function getTagCategory(tag: string): string {
  for (const [category, tags] of Object.entries(PREDEFINED_TAGS)) {
    if (tags.includes(tag)) {
      return category;
    }
  }
  return 'others';
}

/**
 * Validates and processes document summary tags
 */
export function processDocumentTags(rawTags: any): string[] {
  try {
    let tags: string[] = [];
    
    if (Array.isArray(rawTags)) {
      tags = rawTags.filter(tag => typeof tag === 'string');
    } else if (typeof rawTags === 'string') {
      tags = [rawTags];
    } else {
      return ['Others'];
    }
    
    return validateTags(tags);
  } catch (error) {
    console.error('Error processing document tags:', error);
    return ['Others'];
  }
}

/**
 * Example usage and testing
 */
export function testTagValidation() {
  const testTags = [
    "Quarterly Results",           // Exact match
    "quarterly results",           // Case insensitive
    "Q1 Results",                 // Partial match
    "Revenue Growth",             // Exact match
    "Margin Expansion",           // Exact match
    "Operational Efficiency",     // Exact match
    "Custom Tag",                 // No match -> Others
    "Random Text",                // No match -> Others
    "",                           // Empty -> Others
    null,                         // Null -> Others
  ];
  
  console.log('Tag Validation Test Results:');
  testTags.forEach(tag => {
    const validated = validateTag(tag as string);
    const category = getTagCategory(validated);
    console.log(`"${tag}" -> "${validated}" (${category})`);
  });
}

// Export for use in backend processing
export default {
  validateTag,
  validateTags,
  processDocumentTags,
  getTagCategory,
  PREDEFINED_TAGS,
  ALL_VALID_TAGS
};
