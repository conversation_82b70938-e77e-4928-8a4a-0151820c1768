import { NextRequest } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { ReorderItemsRequestSchema } from '@/lib/watchlist/schemas';

export async function POST(req: NextRequest) {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (!supabaseUrl || !serviceKey) {
      return new Response(JSON.stringify({ error: 'Missing Supabase env' }), { status: 500, headers: { 'content-type': 'application/json' } });
    }
    const supabase = createClient(supabaseUrl, serviceKey);

    const json = await req.json();
    const parsed = ReorderItemsRequestSchema.safeParse(json);
    if (!parsed.success) {
      return new Response(JSON.stringify({ error: parsed.error.flatten() }), { status: 400, headers: { 'content-type': 'application/json' } });
    }
    const { watchlist_id, ordered_ids } = parsed.data;

    // Transactional RPC
    const { error } = await supabase.rpc('reorder_watchlist_items', { _watchlist_id: watchlist_id, _ordered_ids: ordered_ids });
    if (error) throw error;

    return new Response(JSON.stringify({ success: true }), { status: 200, headers: { 'content-type': 'application/json' } });
  } catch (e: any) {
    return new Response(JSON.stringify({ error: e?.message || 'Server error' }), { status: 500, headers: { 'content-type': 'application/json' } });
  }
}

