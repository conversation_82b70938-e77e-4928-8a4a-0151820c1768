<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StrikeDeck - My Watchlist</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts: Poppins -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Ionicons for icons -->
    <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
    <!-- AlpineJS for dropdown interactivity -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        // Customizing Tailwind CSS with the new color scheme
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'strikedeck-blue': {
                            'light': '#f1f5f9', // slate-100 for active link background
                            'DEFAULT': '#2c374b', // The brand color
                            'dark': '#3b4a61' // A slightly lighter shade for hover
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* Use Poppins as the default font */
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafc; /* A neutral, light background */
        }
        /* Custom scrollbar for a more refined look */
        ::-webkit-scrollbar {
            width: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        ::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }
        /* Style for the active category link */
        .nav-link.active {
            background-color: #f1f5f9; /* strikedeck-blue-light (slate-100) */
            color: #2c374b; /* strikedeck-blue */
            font-weight: 600;
        }
        .main-content {
            height: 100vh;
            overflow-y: auto;
        }
        /* Additional style for table hover */
        .table-row-hover:hover {
            background-color: #f8fafc; /* bg-slate-50 */
        }
        /* Styles for drag-and-drop functionality */
        .dragging {
            opacity: 0.5;
            background: #e2e8f0; /* slate-200 */
        }
        .drag-handle {
            cursor: move;
        }
    </style>
</head>
<body class="text-slate-700">

    <div class="grid grid-cols-12 min-h-screen bg-white">
        <!-- Left Sidebar -->
        <aside class="col-span-2 border-r border-slate-200 p-6 flex flex-col justify-between">
            <div>
                <div class="flex items-center gap-3 mb-10">
                    <div class="w-10 h-10 bg-strikedeck-blue rounded-lg flex items-center justify-center text-white shadow-md">
                         <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                           <circle cx="12" cy="12" r="7.5" />
                           <path stroke-linecap="round" d="M12 2v4m0 12v4m-8-8H2m20 0h-4" />
                         </svg>
                    </div>
                    <h1 class="font-bold text-xl text-slate-800">StrikeDeck</h1>
                </div>

                <h2 class="text-xs font-semibold text-slate-400 uppercase tracking-wider mb-3">Menu</h2>
                <nav class="space-y-2">
                    <a href="strikedeck_frontendV3.html" class="nav-link flex items-center gap-3 p-2.5 rounded-lg text-slate-600 hover:bg-slate-100 transition-colors">
                        <ion-icon name="newspaper-outline" class="text-xl"></ion-icon>
                        <span>Feed</span>
                    </a>
                    <a href="strikedeck_watchlistV1.html" class="nav-link active flex items-center gap-3 p-2.5 rounded-lg text-slate-600 hover:bg-slate-100 transition-colors">
                        <ion-icon name="star-outline" class="text-xl"></ion-icon>
                        <span>Watchlist</span>
                    </a>
                    <a href="strikedeck_portfolioV2.html" class="nav-link flex items-center gap-3 p-2.5 rounded-lg text-slate-600 hover:bg-slate-100 transition-colors">
                        <ion-icon name="briefcase-outline" class="text-xl"></ion-icon>
                        <span>Portfolio</span>
                    </a>
                    <a href="#" class="nav-link flex items-center gap-3 p-2.5 rounded-lg text-slate-600 hover:bg-slate-100 transition-colors">
                        <ion-icon name="calendar-outline" class="text-xl"></ion-icon>
                        <span>Events Calendar</span>
                    </a>
                </nav>
            </div>
            <div class="space-y-2">
                 <a href="#" class="nav-link flex items-center gap-3 p-2.5 rounded-lg text-slate-600 hover:bg-slate-100 transition-colors">
                    <ion-icon name="settings-outline" class="text-xl"></ion-icon>
                    <span>Settings</span>
                </a>
                 <a href="#" class="nav-link flex items-center gap-3 p-2.5 rounded-lg text-slate-600 hover:bg-slate-100 transition-colors">
                    <ion-icon name="help-circle-outline" class="text-xl"></ion-icon>
                    <span>Help</span>
                </a>
            </div>
        </aside>

        <!-- Main Content Area -->
        <div class="col-span-10 bg-slate-50 main-content relative">
            <header class="sticky top-0 bg-slate-50/80 backdrop-blur-sm z-10 flex justify-between items-center p-8 border-b border-slate-200">
                <div class="relative w-full max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <ion-icon name="search" class="text-slate-400"></ion-icon>
                    </div>
                    <input type="text" placeholder="Search..." class="w-full bg-white border border-slate-300 rounded-lg py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-slate-400 transition">
                </div>
                <div x-data="{ open: false }" class="relative">
                    <button @click="open = !open" class="flex items-center gap-3 text-slate-600 hover:text-slate-800 transition-colors">
                        <span class="font-semibold text-sm hidden sm:inline">John Doe</span>
                        <ion-icon name="person-circle-outline" class="text-3xl"></ion-icon>
                        <ion-icon name="chevron-down-outline" class="text-sm transition-transform" :class="{'rotate-180': open}"></ion-icon>
                    </button>
                    <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl z-20 py-1 border border-slate-200">
                        <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">My Account</a>
                        <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">Settings</a>
                        <hr class="border-slate-200 my-1">
                        <a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50">Logout</a>
                    </div>
                </div>
            </header>

            <!-- Watchlist Content -->
            <div class="p-8" x-data="{ activeView: 'watchlist', selectedWatchlist: 'Tech Stocks', watchlistOpen: false }">
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-2xl font-bold text-slate-800">My Watchlists</h2>
                        <p class="text-slate-500">Track your favorite stocks and market movers.</p>
                    </div>
                    <button class="flex items-center gap-2 bg-strikedeck-blue text-white font-semibold px-4 py-2 rounded-lg hover:bg-strikedeck-blue-dark transition-colors shadow-sm">
                        <ion-icon name="add-outline"></ion-icon>
                        <span>Create Watchlist</span>
                    </button>
                </div>

                <!-- Watchlist Tabs & Dropdown -->
                <div class="mb-6 border-b border-slate-200 flex justify-between items-center">
                    <nav class="flex space-x-2">
                        <button @click="activeView = 'all'" :class="{'border-strikedeck-blue text-strikedeck-blue': activeView === 'all', 'border-transparent text-slate-500 hover:text-slate-700': activeView !== 'all'}" class="px-3 py-3 font-semibold text-sm border-b-2 transition-colors">
                            All My Companies
                        </button>
                        <div class="relative" @click.away="watchlistOpen = false">
                            <button @click="watchlistOpen = !watchlistOpen; activeView = 'watchlist'" :class="{'border-strikedeck-blue text-strikedeck-blue': activeView === 'watchlist', 'border-transparent text-slate-500 hover:text-slate-700': activeView !== 'watchlist'}" class="flex items-center gap-2 px-3 py-3 font-semibold text-sm border-b-2 transition-colors">
                                <span x-text="selectedWatchlist"></span>
                                <ion-icon name="chevron-down-outline" class="text-xs transition-transform" :class="{'rotate-180': watchlistOpen}"></ion-icon>
                            </button>
                            <div x-show="watchlistOpen" x-transition class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-xl z-20 py-1 border border-slate-200">
                                <a href="#" @click="selectedWatchlist = 'Tech Stocks'; watchlistOpen = false" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">Tech Stocks</a>
                                <a href="#" @click="selectedWatchlist = 'FMCG Watch'; watchlistOpen = false" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">FMCG Watch</a>
                                <a href="#" @click="selectedWatchlist = 'New Energy'; watchlistOpen = false" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">New Energy</a>
                            </div>
                        </div>
                    </nav>
                </div>

                <!-- Stocks Table -->
                <div class="bg-white border border-slate-200 rounded-xl shadow-sm">
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left text-slate-500">
                            <thead class="text-xs text-slate-700 uppercase bg-slate-100">
                                <tr>
                                    <th scope="col" class="px-2 py-4 w-12"></th><!-- Drag Handle -->
                                    <th scope="col" class="px-6 py-4 font-semibold">Company</th>
                                    <th scope="col" class="px-6 py-4 font-semibold text-right">Last Price</th>
                                    <th scope="col" class="px-6 py-4 font-semibold text-right">Change</th>
                                    <th scope="col" class="px-6 py-4 font-semibold text-right">Market Cap</th>
                                    <th scope="col" class="px-6 py-4 font-semibold">52-Wk Range</th>
                                    <th scope="col" class="px-6 py-4 font-semibold text-right w-16"></th><!-- Actions -->
                                </tr>
                            </thead>
                            <tbody id="watchlist-table-body">
                                <!-- Sample Row 1 -->
                                <tr class="border-b border-slate-200 table-row-hover" draggable="true">
                                    <td class="px-4 py-4 text-slate-400 drag-handle">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                                            <circle cx="9" cy="6" r="1.5"></circle>
                                            <circle cx="15" cy="6" r="1.5"></circle>
                                            <circle cx="9" cy="12" r="1.5"></circle>
                                            <circle cx="15" cy="12" r="1.5"></circle>
                                            <circle cx="9" cy="18" r="1.5"></circle>
                                            <circle cx="15" cy="18" r="1.5"></circle>
                                        </svg>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="font-semibold text-slate-800">Tata Consultancy</div>
                                        <div class="text-xs text-slate-500">TCS</div>
                                    </td>
                                    <td class="px-6 py-4 font-medium text-slate-800 text-right">3,850.50</td>
                                    <td class="px-6 py-4 text-right">
                                        <div class="font-semibold text-green-600">+1.19%</div>
                                        <div class="text-xs text-slate-500">+45.20</div>
                                    </td>
                                    <td class="px-6 py-4 text-right">14.2T</td>
                                    <td class="px-6 py-4">
                                        <div class="w-full bg-slate-200 rounded-full h-1.5"><div class="bg-gradient-to-r from-strikedeck-blue-dark to-strikedeck-blue h-1.5 rounded-full" style="width: 75%"></div></div>
                                    </td>
                                    <td class="px-6 py-4 text-right" x-data="{ open: false }">
                                        <div class="relative">
                                            <button @click="open = !open" class="text-slate-400 hover:text-strikedeck-blue p-1 rounded-md transition-colors"><ion-icon name="ellipsis-vertical" class="text-lg"></ion-icon></button>
                                            <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-32 bg-white rounded-lg shadow-xl z-20 py-1 border border-slate-200">
                                                <a href="#" class="flex items-center gap-2 px-4 py-2 text-sm text-red-600 hover:bg-red-50"><ion-icon name="trash-outline"></ion-icon>Delete</a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <!-- Sample Row 2 -->
                                 <tr class="border-b border-slate-200 table-row-hover" draggable="true">
                                    <td class="px-4 py-4 text-slate-400 drag-handle">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                                            <circle cx="9" cy="6" r="1.5"></circle>
                                            <circle cx="15" cy="6" r="1.5"></circle>
                                            <circle cx="9" cy="12" r="1.5"></circle>
                                            <circle cx="15" cy="12" r="1.5"></circle>
                                            <circle cx="9" cy="18" r="1.5"></circle>
                                            <circle cx="15" cy="18" r="1.5"></circle>
                                        </svg>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="font-semibold text-slate-800">Infosys Ltd.</div>
                                        <div class="text-xs text-slate-500">INFY</div>
                                    </td>
                                    <td class="px-6 py-4 font-medium text-slate-800 text-right">1,620.10</td>
                                    <td class="px-6 py-4 text-right">
                                        <div class="font-semibold text-red-600">-0.78%</div>
                                        <div class="text-xs text-slate-500">-12.80</div>
                                    </td>
                                    <td class="px-6 py-4 text-right">6.8T</td>
                                    <td class="px-6 py-4">
                                        <div class="w-full bg-slate-200 rounded-full h-1.5"><div class="bg-gradient-to-r from-strikedeck-blue-dark to-strikedeck-blue h-1.5 rounded-full" style="width: 85%"></div></div>
                                    </td>
                                     <td class="px-6 py-4 text-right" x-data="{ open: false }">
                                        <div class="relative">
                                            <button @click="open = !open" class="text-slate-400 hover:text-strikedeck-blue p-1 rounded-md transition-colors"><ion-icon name="ellipsis-vertical" class="text-lg"></ion-icon></button>
                                            <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-32 bg-white rounded-lg shadow-xl z-20 py-1 border border-slate-200">
                                                <a href="#" class="flex items-center gap-2 px-4 py-2 text-sm text-red-600 hover:bg-red-50"><ion-icon name="trash-outline"></ion-icon>Delete</a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <!-- Sample Row 3 -->
                                <tr class="border-b border-slate-200 table-row-hover" draggable="true">
                                    <td class="px-4 py-4 text-slate-400 drag-handle">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                                            <circle cx="9" cy="6" r="1.5"></circle>
                                            <circle cx="15" cy="6" r="1.5"></circle>
                                            <circle cx="9" cy="12" r="1.5"></circle>
                                            <circle cx="15" cy="12" r="1.5"></circle>
                                            <circle cx="9" cy="18" r="1.5"></circle>
                                            <circle cx="15" cy="18" r="1.5"></circle>
                                        </svg>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="font-semibold text-slate-800">Wipro Ltd.</div>
                                        <div class="text-xs text-slate-500">WIPRO</div>
                                    </td>
                                    <td class="px-6 py-4 font-medium text-slate-800 text-right">480.75</td>
                                    <td class="px-6 py-4 text-right">
                                        <div class="font-semibold text-green-600">+0.44%</div>
                                        <div class="text-xs text-slate-500">+2.10</div>
                                    </td>
                                    <td class="px-6 py-4 text-right">2.5T</td>
                                    <td class="px-6 py-4">
                                        <div class="w-full bg-slate-200 rounded-full h-1.5"><div class="bg-gradient-to-r from-strikedeck-blue-dark to-strikedeck-blue h-1.5 rounded-full" style="width: 60%"></div></div>
                                    </td>
                                    <td class="px-6 py-4 text-right" x-data="{ open: false }">
                                        <div class="relative">
                                            <button @click="open = !open" class="text-slate-400 hover:text-strikedeck-blue p-1 rounded-md transition-colors"><ion-icon name="ellipsis-vertical" class="text-lg"></ion-icon></button>
                                            <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-32 bg-white rounded-lg shadow-xl z-20 py-1 border border-slate-200">
                                                <a href="#" class="flex items-center gap-2 px-4 py-2 text-sm text-red-600 hover:bg-red-50"><ion-icon name="trash-outline"></ion-icon>Delete</a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                 <!-- Sample Row 4 -->
                                <tr class="border-b-0 table-row-hover" draggable="true">
                                    <td class="px-4 py-4 text-slate-400 drag-handle">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                                            <circle cx="9" cy="6" r="1.5"></circle>
                                            <circle cx="15" cy="6" r="1.5"></circle>
                                            <circle cx="9" cy="12" r="1.5"></circle>
                                            <circle cx="15" cy="12" r="1.5"></circle>
                                            <circle cx="9" cy="18" r="1.5"></circle>
                                            <circle cx="15" cy="18" r="1.5"></circle>
                                        </svg>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="font-semibold text-slate-800">HCL Technologies</div>
                                        <div class="text-xs text-slate-500">HCLTECH</div>
                                    </td>
                                    <td class="px-6 py-4 font-medium text-slate-800 text-right">1,455.00</td>
                                    <td class="px-6 py-4 text-right">
                                        <div class="font-semibold text-red-600">-0.36%</div>
                                        <div class="text-xs text-slate-500">-5.30</div>
                                    </td>
                                    <td class="px-6 py-4 text-right">3.9T</td>
                                    <td class="px-6 py-4">
                                        <div class="w-full bg-slate-200 rounded-full h-1.5"><div class="bg-gradient-to-r from-strikedeck-blue-dark to-strikedeck-blue h-1.5 rounded-full" style="width: 90%"></div></div>
                                    </td>
                                    <td class="px-6 py-4 text-right" x-data="{ open: false }">
                                        <div class="relative">
                                            <button @click="open = !open" class="text-slate-400 hover:text-strikedeck-blue p-1 rounded-md transition-colors"><ion-icon name="ellipsis-vertical" class="text-lg"></ion-icon></button>
                                            <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-32 bg-white rounded-lg shadow-xl z-20 py-1 border border-slate-200">
                                                <a href="#" class="flex items-center gap-2 px-4 py-2 text-sm text-red-600 hover:bg-red-50"><ion-icon name="trash-outline"></ion-icon>Delete</a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Floating Action Button -->
            <div class="absolute bottom-8 right-8 z-20 group">
                <button title="Add Company" class="bg-strikedeck-blue text-white w-14 h-14 rounded-full flex items-center justify-center shadow-lg hover:bg-strikedeck-blue-dark transition-transform transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-strikedeck-blue">
                    <ion-icon name="add-outline" class="text-2xl"></ion-icon>
                </button>
            </div>

        </div>
    </div>
    <script>
        // JavaScript for drag-and-drop functionality
        document.addEventListener('DOMContentLoaded', () => {
            const tableBody = document.getElementById('watchlist-table-body');
            let draggingEle;

            const onDragStart = function (e) {
                draggingEle = e.target.closest('tr');
                if (!draggingEle) return;
                draggingEle.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/plain', null);
            };

            const onDragOver = function (e) {
                e.preventDefault();
                const target = e.target.closest('tr');
                if (target && target !== draggingEle) {
                    const rect = target.getBoundingClientRect();
                    const next = (e.clientY - rect.top) / (rect.bottom - rect.top) > .5;
                    tableBody.insertBefore(draggingEle, next && target.nextSibling || target);
                }
            };
            
            const onDragEnd = function (e) {
                if (draggingEle) {
                    draggingEle.classList.remove('dragging');
                }
                draggingEle = null;
            };

            tableBody.addEventListener('dragstart', onDragStart);
            tableBody.addEventListener('dragover', onDragOver);
            tableBody.addEventListener('dragend', onDragEnd);
        });
    </script>
</body>
</html>
