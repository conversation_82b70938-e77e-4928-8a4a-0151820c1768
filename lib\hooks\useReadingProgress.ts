'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface ReadingProgress {
  lastReadFilingId?: string;
  readFilingIds: string[];
  lastVisit?: string;
}

const STORAGE_KEY = 'corporate_filings_reading_progress';

export function useReadingProgress() {
  const { user } = useAuth();
  const [progress, setProgress] = useState<ReadingProgress>({
    readFilingIds: []
  });
  const [isNewSession, setIsNewSession] = useState(true);

  // Load progress from localStorage on mount
  useEffect(() => {
    if (!user) return;

    const stored = localStorage.getItem(`${STORAGE_KEY}_${user.id}`);
    if (stored) {
      try {
        const parsedProgress = JSON.parse(stored);
        setProgress(parsedProgress);
      } catch (error) {
        console.error('Error parsing reading progress:', error);
      }
    }
  }, [user]);

  // Save progress to localStorage
  const saveProgress = useCallback((newProgress: ReadingProgress) => {
    if (!user) return;

    setProgress(newProgress);
    localStorage.setItem(`${STORAGE_KEY}_${user.id}`, JSON.stringify(newProgress));
  }, [user]);

  // Mark filing as read
  const markFilingAsRead = useCallback((filingId: string) => {
    if (!user) return;

    const newProgress = {
      ...progress,
      lastReadFilingId: filingId,
      readFilingIds: [...new Set([...progress.readFilingIds, filingId])]
    };

    saveProgress(newProgress);
  }, [progress, saveProgress, user]);

  // Update last visit time
  const updateLastVisit = useCallback(() => {
    if (!user) return;

    const newProgress = {
      ...progress,
      lastVisit: new Date().toISOString()
    };

    saveProgress(newProgress);
    setIsNewSession(false);
  }, [progress, saveProgress, user]);

  // Check if filing is new (created after last visit)
  const isFilingNew = useCallback((filingCreatedAt: string) => {
    if (!progress.lastVisit) return true;

    const filingDate = new Date(filingCreatedAt);
    const lastVisitDate = new Date(progress.lastVisit);

    return filingDate > lastVisitDate;
  }, [progress.lastVisit]);

  // Check if filing was read
  const isFilingRead = useCallback((filingId: string) => {
    return progress.readFilingIds.includes(filingId);
  }, [progress.readFilingIds]);

  // Get the position where user left off (index of last read filing)
  const getLastReadPosition = useCallback((filings: any[]) => {
    if (!progress.lastReadFilingId) return -1;

    return filings.findIndex(filing => filing.id === progress.lastReadFilingId);
  }, [progress.lastReadFilingId]);

  return {
    progress,
    isNewSession,
    markFilingAsRead,
    updateLastVisit,
    isFilingNew,
    isFilingRead,
    getLastReadPosition
  };

  // Optional: persist last-read to backend later; current impl is localStorage per user

}